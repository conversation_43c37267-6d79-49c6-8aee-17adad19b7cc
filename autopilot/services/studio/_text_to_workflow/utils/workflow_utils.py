import copy
import difflib
import hashlib
import re

import <PERSON><PERSON>htein
import typing_extensions as t

from services.studio._text_to_workflow.common.activity_utils import remove_fields
from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflow
from services.studio._text_to_workflow.common.constants import (
    DELEGATE_VARIABLES_KEY,
    MANUAL_TRIGGER,
    SEQUENCE_ACTIVITY_NAME,
    SEQUENCE_GENERATION_INSERTION_PHRASE,
)
from services.studio._text_to_workflow.common.schema import ActivityDict, WorkflowDict
from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils import errors
from services.studio._text_to_workflow.utils.json_utils import json_dump
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import YAM<PERSON>rror, yaml_load
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import core_wf_workflow_edit_score

LOGGER = AppInsightsLogger()


indexing_re = re.compile(r"^\s*(\d+\.)*\s*")
indentation_re = re.compile(r"^\s*")


def strip_step(step: str) -> str:
    return indexing_re.sub("", step)


def get_display_names(plan: str) -> list[str]:
    return [strip_step(step) for step in plan.split("\n")]


def get_diff(a: str, b: str, span: int | None = None) -> str:
    lines = list(difflib.ndiff(a.splitlines(keepends=True), b.splitlines(keepends=True)))
    if span is not None:
        important_ones = {i for i, line in enumerate(lines) if any(line.startswith(c) for c in "+-?")}
        important_ones |= {j for i in list(important_ones) for j in range(i - span, i + span + 1)}
        lines = [line for i, line in enumerate(lines) if i in important_ones]
    return "".join(lines)


def get_plan_structure(plan: str) -> str:
    lines = plan.strip().split("\n")
    return "\n".join(m.group(0) if (m := indexing_re.search(line)) else "None" for line in lines)


def renumber_plan_steps(plan: str, deindent_common_indentation: bool = True) -> str:
    plan_steps = [line for line in plan.splitlines() if line]

    if deindent_common_indentation:
        indentations = [match.group() if (match := indentation_re.search(line)) is not None else "" for line in plan_steps]
        common_indentation_size = min(map(len, indentations), default=0)
        plan_steps = [step[common_indentation_size:] for step in plan_steps]

    new_plan_steps = []
    step_content_re = re.compile(r"\s*(\d+\.)+\s(?P<content>.*)")
    counters, previous_indentation = [0], ""
    for step in plan_steps:
        indentation_match = indentation_re.search(step)
        if indentation_match is None:
            LOGGER.warning(f"Skipping step with no matching indentation: {step}")
            continue
        indentation = indentation_match.group()

        if len(indentation) > len(previous_indentation):
            counters.append(0)
        elif len(indentation) < len(previous_indentation):
            counters.pop()
        counters[-1] += 1
        prefix = ".".join(map(str, counters))

        content_match = step_content_re.search(step)
        if content_match is None:
            LOGGER.warning("Skipping step with no matching content: {step}")
            continue
        content = content_match.group("content")

        new_plan_steps.append(f"{indentation}{prefix}. {content}")
        previous_indentation = indentation
    return "\n".join(new_plan_steps)


def iterate_nodes(node: object, ancestry: list[str] | None = None, variables: list[dict] | None = None, **kwargs):
    """Iterate over all nodes in the workflow, yielding the node, its ancestry and local variables."""
    # These functions are good too keep for testing on yamls without using the workflow abstraction
    include_only_activities_in_ancestry = kwargs.get("include_only_activities_in_ancestry", False)
    if ancestry is None:
        ancestry = []
    if variables is None:
        variables = []
    if isinstance(node, list):
        for child in node:
            yield from iterate_nodes(child, ancestry=ancestry, variables=variables, **kwargs)
        return
    if not isinstance(node, dict):
        return

    yield node, ancestry, variables

    has_activity = "activity" in node
    new_variables = node.get(DELEGATE_VARIABLES_KEY, [])
    for key, child in node.items():
        include_in_ancestry = not include_only_activities_in_ancestry or has_activity
        child_ancestry = ancestry + [node.get("activity", key)] * include_in_ancestry
        child_variables = variables + new_variables
        yield from iterate_nodes(child, ancestry=child_ancestry, variables=child_variables, **kwargs)


def iterate_activities(root_node: WorkflowDict):
    for node, ancestry, variables in iterate_nodes(root_node, include_only_activities_in_ancestry=True):
        if "activity" in node:
            yield node, ancestry, variables


def has_inner_activity(root_node: WorkflowDict):
    return any(iterate_activities(root_node))  # assumption that the yields are not False equivalents


def iterate_and_apply(root_node: WorkflowDict, func: t.Callable):
    return [func(*args) for args in iterate_nodes(root_node)]


def get_workflow_summary(workflow: WorkflowDict) -> str:
    """Get a text tree summary of the workflow."""
    if isinstance(workflow, str):
        workflow = yaml_load(workflow)
    summary = []
    for node, ancestry, _variables in iterate_activities(workflow):
        activity = node["activity"]
        thought = node.get("thought", node.get("displayName", "no thought"))
        params = node.get("params", {})
        params = {k: v for k, v in params.items() if not has_inner_activity(v)}
        params = [f"{k}: {v if len(str(v)) < 100 else str(v)[:100] + ' ...'}" for k, v in params.items()]
        indent = "  " * len(ancestry)
        summary.append(f"{indent}{activity} ({thought}) | {' | '.join(params)}")
    return "\n".join(summary)


def deepsort(x):
    """Recursively sort dictionary's keys. Makes the assumption keys are strings."""
    # can be extended for object keys that are implementing total ordering
    if isinstance(x, list):
        return [deepsort(v) for v in x]
    if isinstance(x, dict):
        exclusions = {}
        # exclusions = {"DisplayName", "Target", "TargetApp", "__type"}  # used for comparing workflows
        # x = {str(k): v for k, v in x.items()}  # forcing keys to strings (sometimes bools are encountered as keys in workflow yaml)
        assert all(isinstance(k, str) for k in x), f"not all strings:{list(x.keys())}"
        return {k: deepsort(v) for k, v in sorted(x.items()) if k not in exclusions}
    return x


def convert_implicit_sequences_to_explicit_sequences(node: dict) -> dict:
    if not isinstance(node, dict) and not isinstance(node, list):
        return node

    if isinstance(node, dict):
        for key, value in node.items():
            node[key] = convert_implicit_sequences_to_explicit_sequences(value)

    if isinstance(node, list):
        length = len(node)

        for idx, elem in enumerate(node):
            node[idx] = convert_implicit_sequences_to_explicit_sequences(elem)

        # For an activity list, return a sequence instead
        if length != 0 and isinstance(node[0], dict) and "activity" in node[0]:
            return {"thought": "Sequence", "activity": SEQUENCE_ACTIVITY_NAME, "params": {"Activities": node}}

    # Otherwise, just return the node
    return node


def build_workflow_from_sample(process):
    workflow = {}
    existing_ids = dict[str, int]()

    if "trigger" in process and process["trigger"]["activity"] != "UiPath.Core.Activities.ManualTrigger":
        workflow["trigger"] = process["trigger"]
        add_workflow_ids(workflow["trigger"], existing_ids)

    if "workflow" in process:
        workflow["workflow"] = process["workflow"]
        if isinstance(workflow["workflow"], list):
            for activity in workflow["workflow"]:
                add_workflow_ids(activity, existing_ids)
        elif isinstance(workflow["workflow"], dict):
            add_workflow_ids(workflow["workflow"], existing_ids)

    if "variables" in process:
        workflow["variables"] = process["variables"]

    if "arguments" in process:
        workflow["arguments"] = process["arguments"]
    return workflow


def remove_activity_ids(node: dict) -> None:
    """Remove activity IDs from a workflow."""

    # Remove id if this is an activity node
    if "activity" in node and "id" in node:
        del node["id"]

    # Recursively process all dictionary values and list elements
    for value in node.values():
        if isinstance(value, dict):
            remove_activity_ids(value)
        elif isinstance(value, list):
            for elem in value:
                if isinstance(elem, dict):
                    remove_activity_ids(elem)


def add_workflow_ids(node: dict, existing_ids: dict[str, int] = {}) -> None:
    if not isinstance(node, dict):
        LOGGER.warning(f"Node is not dict but a {type(node)}")
        return

    is_activity = "activity" in node

    if is_activity:
        # besides settings the id, move the current params so that the id is placed above the params in the output
        params = node["params"] if "params" in node else None
        if "params" in node:
            del node["params"]

        activity_full_name = node["activity"]

        # Remove patterns of type <X> or `X` at the end of the activity name
        activity_full_name = re.sub(r"<[^>]+>$|`[^`]+$", "", activity_full_name)
        activity_name = activity_full_name.split(".")[-1]

        id = existing_ids.get(activity_name, 0) + 1
        existing_ids[activity_name] = id

        node["id"] = activity_name + "_" + str(id)

        if params is not None:
            node["params"] = params

    for value in node.values():
        if isinstance(value, dict):
            add_workflow_ids(value, existing_ids)
        elif isinstance(value, list):
            for elem in value:
                if isinstance(elem, dict):
                    add_workflow_ids(elem, existing_ids)


def load_workflow_instance(query: str, existing_workflow: str, description: str = "") -> Workflow:
    try:
        workflow_lmyaml = yaml_load(existing_workflow)
    except YAMLError:  # invalid yaml
        raise errors.ExistingWorkflowYAMLDeserializationError()
    if not workflow_lmyaml:  # empty workflow
        raise errors.ExistingWorkflowEmptyError()
    if not isinstance(workflow_lmyaml, dict):  # invalid workflow
        raise errors.ExistingWorkflowInvalidError("Workflow is not a dictionary")
    if "workflow" not in workflow_lmyaml:  # no workflow present
        LOGGER.warning("No workflow present in the yaml. Mitigating.")
        workflow_lmyaml["workflow"] = []
    if not isinstance(workflow_lmyaml["workflow"], list):
        LOGGER.warning(f"Workflow is not a list but a {type(workflow_lmyaml['workflow'])}. Mitigating.")
        workflow_lmyaml["workflow"] = []
    if not workflow_lmyaml["workflow"]:  # empty workflow
        LOGGER.warning("Empty workflow present in the yaml. Mitigating.")
        workflow_lmyaml["workflow"].append({"thought": "", "activity": SEQUENCE_ACTIVITY_NAME, "currentActivity": True})
    try:
        workflow_object = Workflow(query, description, t.cast(WorkflowDict, workflow_lmyaml))
    except Exception as e:
        raise errors.ExistingWorkflowInvalidError("Could not load workflow.") from e
    return workflow_object


def load_completion_yaml(output: str, return_none_if_badly_formed: bool = False) -> dict | None:
    loaded = {}
    if output.startswith("```yaml"):
        match = re.match(r"```yaml(.*)```", output, re.DOTALL)
    elif output.startswith("```"):
        match = re.match(r"```(.*)```", output, re.DOTALL)
    else:
        match = re.match(r"(.*)", output, re.DOTALL)
    if not match:
        return loaded
    try:
        process_str = match.group(1)
    except IndexError:
        LOGGER.warning("IndexError")
        return loaded

    try:
        loaded = yaml_load(process_str)
        if not isinstance(loaded, dict):
            loaded = {}
    except YAMLError:
        if return_none_if_badly_formed:
            return None

        LOGGER.warning("YAMLError in output. Trying to fix single quotes.")
        lines = process_str.splitlines()
        lines_fixed = []
        for line in lines:
            parts = line.split(": ", 1)
            if len(parts) > 1:
                if not parts[1].strip().startswith("'"):
                    line = f"{parts[0]}: '{parts[1]}"
                if not parts[1].strip().endswith("'"):
                    line += "'"
                line = line.replace("\\'", "''")
            lines_fixed.append(line)
        process_str = "\n".join(lines_fixed)
        try:
            loaded = yaml_load(process_str)
        except YAMLError:
            LOGGER.warning("WARNING: YAMLError in output. Failed to fix single quotes.")
            return loaded
    return loaded


def use_typeid_in_activities(workflow: WorkflowDict | ActivityDict):
    # use type id to distinguish DAP activities
    if isinstance(workflow, dict):
        if "activity" in workflow:
            if "params" in workflow and "UiPathActivityTypeId" in workflow["params"]:
                workflow["activity"] += " " + workflow["params"]["UiPathActivityTypeId"]
            elif "uiPathActivityTypeId" in workflow:
                workflow["activity"] += " " + workflow["uiPathActivityTypeId"]
        for key in workflow:
            use_typeid_in_activities(workflow[key])
    if isinstance(workflow, list):
        for i in workflow:
            use_typeid_in_activities(i)


def replace_blobs_with_hash(text: str, min_length: int = 100, use_b64_charset: bool = False) -> tuple[str, dict[str, str]]:
    """
    Function to replace sequences of characters in a string with their hash values.

    Args:
        text (str): The input text string.
        min_length (int, optional): Minimum length of sequence to replace. Defaults to 100.
        use_b64_charset (bool, optional): Whether to use base64 charset pattern. Defaults to False.

    Returns:
        tuple[str, dict[str, str]]: A tuple containing:
            - The modified string with hash values instead of continuous characters
            - A mapping dictionary with hash values as keys and original text as values

    Example usage:
        text = "Some text with a long blob: ABCDEF12345678900987654321ABCDEF"
        modified_text, mapping = replace_blobs_with_hash(text, min_length=20)
        # Use the modified text for processing
        # Later, restore the original content
        original_text = revert_blobs_from_hash(modified_text, mapping)
    """

    # Define a regular expression pattern to match continuous characters
    if use_b64_charset:
        pattern = rf"\b[a-zA-Z0-9+/]{{{min_length},}}\b"
    else:
        pattern = rf"\b[a-zA-Z0-9]{{{min_length},}}\b"

    # Find all matches of the pattern in the text
    matches = re.findall(pattern, text)
    # Replace each match with its hash value
    mapping = {}
    for match in matches:
        hash_value = hashlib.sha256(match.encode()).hexdigest()
        replacement = f"{hash_value}"
        text = text.replace(match, replacement)
        mapping[replacement] = match
    return text, mapping


def revert_blobs_from_hash(text: str, mapping: dict[str, str]) -> str:
    """
    Function to restore the original text from a string with hash replacements.

    Args:
        text (str): The text containing hash replacements.
        mapping (dict[str, str]): A mapping dictionary with hash values as keys and original text as values.
                                  This is typically the second return value from replace_blobs_with_hash.

    Returns:
        str: The original text with hash values replaced back to their original content.
    """
    for hash_value, original_text in mapping.items():
        text = text.replace(hash_value, original_text)
    return text


def normalize_activity_dep(activity):
    if "<" in activity:
        return f"{activity.split('<')[0]}`1"
    return activity


def use_normalized_activity_dep(workflow):
    # use type id to distinguish DAP activities
    if isinstance(workflow, dict):
        if "activity" in workflow:
            workflow["activity"] = normalize_activity_dep(workflow["activity"])
        for key in workflow:
            use_normalized_activity_dep(workflow[key])
    if isinstance(workflow, list):
        for i in workflow:
            use_normalized_activity_dep(i)


def cleanup_api_workflow_for_compare(workflow: dict) -> dict:
    result = _cleanup_workflow_for_compare(workflow, {"thought", "activityId"})
    assert isinstance(result, dict)
    return result


def cleanup_rpa_wf_workflow_for_compare(workflow: dict | WorkflowDict) -> dict:
    result = _cleanup_workflow_for_compare(workflow, {"thought", "processName", "packages", "variables", "namespaceImports"})
    assert isinstance(result, dict)
    return t.cast(dict, result)


def _cleanup_workflow_for_compare(workflow, keys_to_remove: set[str]):
    return remove_fields(workflow, keys_to_remove)


def _test_renumbering():
    """Simple test for renumber_plan_steps."""
    plan = """\
1. For each item in the JSON array
  1.1. Log the name from the current JSON token
  1.2. Log the ID from the current JSON token"""
    assert plan == renumber_plan_steps(plan.replace("1.1.", "5.3.7."))


def _clean_workflow_dap_keys(workflow: WorkflowDict | list) -> WorkflowDict | list:
    return remove_fields(
        workflow, ["configuration", "connectorKey", "dynamicActivityDetails", "isConfigured", "isDynamic", "uiPathActivityTypeId", "UiPathActivityTypeId"]
    )


def cleanup_rpa_workflow_for_tld(workflow: WorkflowDict) -> str:
    """Prepare a workflow for the Triangle Levenshtein Distance metric."""
    workflow_object = Workflow("", "", workflow)
    serialization = workflow_object.lmyaml(include_packages=False, include_name=False, include_ids=False)
    return serialization


def cleanup_workflow_for_ted(workflow: WorkflowDict, shallow: bool = False) -> WorkflowDict:
    """Cleanup a workflow for the Tree Edit Distance metric. Removes irrelevant fields and coalesces dap activity ids"""
    if not shallow:
        workflow = copy.deepcopy(workflow)
    cleaned_workflow = cleanup_rpa_wf_workflow_for_compare(workflow)
    use_typeid_in_activities(cleaned_workflow)
    use_normalized_activity_dep(cleaned_workflow)
    cleaned_workflow = _clean_workflow_dap_keys(cleaned_workflow)
    assert isinstance(cleaned_workflow, dict)
    return cleaned_workflow


def get_tld_rpa(original: WorkflowDict, reference: WorkflowDict, candidate: WorkflowDict) -> float:
    """Computes the Triangle Levenshtein Distance between three RPA workflows."""
    orig, ref, cand = [cleanup_rpa_workflow_for_tld(wf) for wf in [original, reference, candidate]]
    return get_tld(orig, ref, cand)


def get_tld_api(original: ApiWorkflow, reference: ApiWorkflow, candidate: ApiWorkflow) -> float:
    """Computes the Triangle Levenshtein Distance between three API workflows."""
    orig, ref, cand = [json_dump(wf.root.model_dump(exclude_none=True, by_alias=True)) if wf else "" for wf in [original, reference, candidate]]
    return get_tld(orig, ref, cand)


def get_tld(original: str, reference: str, candidate: str) -> float:
    """Computes the Triangle Levenshtein Distance between three strings."""
    a = Levenshtein.distance(original, reference)
    b = Levenshtein.distance(original, candidate)
    c = Levenshtein.distance(reference, candidate)
    return 1 - c / (a + b + 1e-7)


def get_ted(workflow1: WorkflowDict, workflow2: WorkflowDict) -> float:
    """Computes the Tree Edit Distance between two workflows by node matching."""
    cleaned_workflow1 = cleanup_workflow_for_ted(workflow1)
    cleaned_workflow2 = cleanup_workflow_for_ted(workflow2)
    ted_score, _, _, _ = core_wf_workflow_edit_score(cleaned_workflow1, cleaned_workflow2)
    return ted_score


def get_pled(workflow1: WorkflowDict, workflow2: WorkflowDict) -> float:
    """Computes the Parameter Levenshtein Edit Distance between two workflows by partial node matching with parametrization distance."""
    cleaned_workflow1 = cleanup_workflow_for_ted(workflow1)
    cleaned_workflow2 = cleanup_workflow_for_ted(workflow2)
    pled_score, _, _, _ = core_wf_workflow_edit_score(cleaned_workflow1, cleaned_workflow2, "levenshtein", None)
    return pled_score


def delete_sequence_thought(workflow):
    """Deletes the entire sequence activity containing the sequence thought from a workflow."""
    if isinstance(workflow, dict):
        if workflow.get("thought", "") == SEQUENCE_GENERATION_INSERTION_PHRASE:
            return {}, True
        new_dict = {}
        for k, v in workflow.items():
            node, should_skip = delete_sequence_thought(v)
            if not should_skip:
                new_dict[k] = node
        return new_dict, False
    elif isinstance(workflow, list):
        new_list = []
        for item in workflow:
            node, should_skip = delete_sequence_thought(item)
            if not should_skip:
                new_list.append(node)
        return new_list, False
    return workflow, False


def is_workflow_empty(workflow_instance: Workflow) -> bool:
    """
    Check if a workflow is empty (has no activities or triggers).

    Args:
        workflow_instance: A Workflow instance

    Returns:
        True if the workflow has no activities or triggers, False otherwise
    """
    activities_and_triggers = ActivitiesAndTriggersCollector().collect(workflow_instance)

    if len(activities_and_triggers["trigger"]) > 0 and activities_and_triggers["activity"][0].activity_id != MANUAL_TRIGGER:
        # if there is a trigger and it is not a manual trigger, the workflow is not empty
        return False

    if len(activities_and_triggers["activity"]) > 1:
        # if there is more than one activity, the workflow is not empty
        return False

    if len(activities_and_triggers["activity"]) == 1 and activities_and_triggers["activity"][0].activity_id != SEQUENCE_ACTIVITY_NAME:
        # if there is one activity and it is not a sequence, the workflow is not empty
        return False

    return True
