import json
from typing import Any, Dict, List, Sequence, cast

from services.studio._text_to_workflow.bpmn_evaluation.base_evaluator import BaseEvaluator
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_constants import EvalTypes, ScoreThresholds
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_schema import (
    BPMNJsonPatchDatasetEntry,
    BPMNJsonPatchEvaluationResult,
)
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_utils import enum_serializer
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_json_patch_xml_converter import BPMNPatchApplier
from services.studio._text_to_workflow.bpmn_evaluation.bpmn_xml_ted_evaluator_helpers import BPMNXMLParser, TEDScoreCalculator
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import ModelType, Tool
from services.studio._text_to_workflow.bpmn_generation.bpmn_router_task import BpmnRouterTask
from services.studio._text_to_workflow.models.model_manager import Model<PERSON>anager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType

LOGGER = telemetry_utils.AppInsightsLogger()
TASK: BpmnRouterTask = BpmnRouterTask()


class BaseJSONPatchTEDEvaluator(BaseEvaluator[BPMNJsonPatchDatasetEntry, BPMNJsonPatchEvaluationResult]):
    evaluation_type = "BaseJSONPatchTED"
    result_class = BPMNJsonPatchEvaluationResult
    override_tool = Tool.EDIT_BPMN

    def __init__(self) -> None:
        super().__init__()

    def get_expected_result(self, dataset: BPMNJsonPatchDatasetEntry) -> str:
        if dataset.expectedBPMN_XML:
            return dataset.expectedBPMN_XML

        LOGGER.error(f"No expected BPMN XML for dataset {dataset.id}")
        return ""

    def get_dataset_metadata(self, dataset: BPMNJsonPatchDatasetEntry) -> Dict[str, Any]:
        return {
            "id": dataset.id,
            "dataset_source": dataset.dataset_source,
            "user_prompt": dataset.user_prompt,
            "prompt_verbosity": dataset.prompt_verbosity,
        }

    def load_datasets(self) -> Sequence[BPMNJsonPatchDatasetEntry]:
        raise NotImplementedError("Subclasses must implement load_datasets()")

    async def generate_output(self, dataset: BPMNJsonPatchDatasetEntry) -> Dict[str, Any]:
        user_prompt = dataset.user_prompt
        current_bpmn = dataset.currentBPMN_XML or ""

        if not current_bpmn:
            LOGGER.warning("No current BPMN XML provided. Starting with empty BPMN.")

        try:
            result = await TASK.process(
                {
                    "userRequest": user_prompt,
                    "currentBpmn": current_bpmn,
                    "modelTypeOverride": {"tool": self.override_tool, "model_type": ModelType.Anthropic},
                }
            )
            output = self._get_tool_result(result, tool_name=self.override_tool)
            # Check if output contains an error
            if "error" in output:
                error_msg = output["error"]
                LOGGER.error(error_msg)
                return {
                    "error": error_msg,
                    "eval_score": 0,
                    "generated_result": "",
                    "generated_bpmn_xml": "",
                    "expected_bpmn_xml": "",
                    "node_stats": {
                        "expected_count": 0,
                        "generated_count": 0,
                        "common_count": 0,
                        "missing_count": 0,
                        "extra_count": 0,
                        "missing_nodes": [],
                        "extra_nodes": [],
                    },
                }

            # 2. Convert JSON patch to string for storage and reporting
            try:
                json_patch_data_str = json.dumps(output, indent=2, default=enum_serializer)
            except Exception as e:
                error_msg = f"Error serializing JSON patch: {str(e)}"
                LOGGER.error(error_msg)
                return {
                    "error": error_msg,
                    "eval_score": 0,
                    "generated_result": "",
                    "generated_bpmn_xml": "",
                    "expected_bpmn_xml": "",
                    "node_stats": {
                        "expected_count": 0,
                        "generated_count": 0,
                        "common_count": 0,
                        "missing_count": 0,
                        "extra_count": 0,
                        "missing_nodes": [],
                        "extra_nodes": [],
                    },
                }

            # 3. Extract element IDs for added, updated, and removed elements
            added_elements = self._extract_element_ids(output.get("add") or [])
            updated_elements = self._extract_element_ids(output.get("update") or [])
            removed_elements = self._extract_element_ids(output.get("delete") or [])

            LOGGER.info(f"Elements - Added: {len(added_elements)}, Updated: {len(updated_elements)}, Removed: {len(removed_elements)}")

            # 4. Apply the JSON patch to current BPMN XML to get generated BPMN XML
            try:
                json_patch_dict = {"add": output.get("add", []), "update": output.get("update", []), "delete": output.get("delete", [])}

                if current_bpmn:
                    LOGGER.info(f"Current BPMN XML: {len(current_bpmn)} chars")

                patch_applier = BPMNPatchApplier()
                generated_bpmn_xml = patch_applier.apply_patch(json_patch_dict, current_bpmn)
                LOGGER.info(f"Generated BPMN XML: {len(generated_bpmn_xml)} chars")

            except Exception as e:
                error_msg = f"Error converting JSON patch to BPMN XML: {str(e)}"
                LOGGER.error(error_msg)
                return {
                    "error": error_msg,
                    "eval_score": 0,
                    "generated_result": "",
                    "generated_bpmn_xml": "",
                    "expected_bpmn_xml": "",
                    "node_stats": {
                        "expected_count": 0,
                        "generated_count": 0,
                        "common_count": 0,
                        "missing_count": 0,
                        "extra_count": 0,
                        "missing_nodes": [],
                        "extra_nodes": [],
                    },
                }

            # 5. Return all generated artifacts for evaluation
            return {
                "generated_output": json_patch_data_str,
                "generated_json_patch": output,
                "generated_bpmn_xml": generated_bpmn_xml,
                "added_elements": added_elements,
                "updated_elements": updated_elements,
                "removed_elements": removed_elements,
            }
        except Exception as e:
            error_msg = f"Error generating JSON Patch: {str(e)}"
            LOGGER.error(error_msg)
            return {
                "error": error_msg,
                "eval_score": 0,
                "generated_result": "",
                "generated_bpmn_xml": "",
                "expected_bpmn_xml": "",
                "node_stats": {
                    "expected_count": 0,
                    "generated_count": 0,
                    "common_count": 0,
                    "missing_count": 0,
                    "extra_count": 0,
                    "missing_nodes": [],
                    "extra_nodes": [],
                },
            }

    def calculate_score(self, dataset: BPMNJsonPatchDatasetEntry, output_data: Dict[str, Any]) -> Dict[str, Any]:
        added_elements = output_data.get("added_elements", [])
        updated_elements = output_data.get("updated_elements", [])
        removed_elements = output_data.get("removed_elements", [])

        # 1. Get the expected BPMN XML (ground truth) directly from dataset
        expected_bpmn_xml = self.get_expected_result(dataset)
        if not expected_bpmn_xml:
            LOGGER.error("No expected BPMN XML available in dataset for evaluation")
            return {
                "eval_score": 0,
                "error": "No expected BPMN XML available in dataset for evaluation",
                "added_elements": added_elements,
                "updated_elements": updated_elements,
                "removed_elements": removed_elements,
                "generated_result": output_data.get("generated_output", ""),
                "generated_bpmn_xml": "",
                "expected_bpmn_xml": "",
                "node_stats": {
                    "expected_count": 0,
                    "generated_count": 0,
                    "common_count": 0,
                    "missing_count": 0,
                    "extra_count": 0,
                    "missing_nodes": [],
                    "extra_nodes": [],
                },
            }

        # 2. Get the generated BPMN XML from the output data
        generated_bpmn_xml = output_data.get("generated_bpmn_xml", "")
        if not generated_bpmn_xml:
            LOGGER.error("No generated BPMN XML available for evaluation")
            return {
                "eval_score": 0,
                "error": "No generated BPMN XML available for evaluation",
                "added_elements": added_elements,
                "updated_elements": updated_elements,
                "removed_elements": removed_elements,
                "generated_result": output_data.get("generated_output", ""),
                "generated_bpmn_xml": "",
                "expected_bpmn_xml": expected_bpmn_xml,
                "node_stats": {
                    "expected_count": 0,
                    "generated_count": 0,
                    "common_count": 0,
                    "missing_count": 0,
                    "extra_count": 0,
                    "missing_nodes": [],
                    "extra_nodes": [],
                },
            }

        # 3. Remove bpmndi elements from both expected and generated XML
        expected_bpmn_xml_clean = BPMNXMLParser.remove_bpmndi(expected_bpmn_xml)
        generated_bpmn_xml_clean = BPMNXMLParser.remove_bpmndi(generated_bpmn_xml)

        # 4. Get node IDs from the cleaned XMLs
        expected_node_ids = TEDScoreCalculator.get_all_node_ids(expected_bpmn_xml_clean)
        generated_node_ids = TEDScoreCalculator.get_all_node_ids(generated_bpmn_xml_clean)

        missing_node_ids = expected_node_ids - generated_node_ids
        extra_node_ids = generated_node_ids - expected_node_ids
        common_node_ids = expected_node_ids.intersection(generated_node_ids)

        LOGGER.info(f"Node counts - Expected: {len(expected_node_ids)}, Generated: {len(generated_node_ids)}, Common: {len(common_node_ids)}")

        # 5. Calculate TED score using the cleaned XMLs
        ted_score = 0
        ted_error = None
        # Keep track of added and removed nodes from TED calculation
        added_nodes = []
        removed_nodes = []

        try:
            # Use the compute method that already removes bpmndi elements
            ted_score, added_nodes, removed_nodes, ted_error = TEDScoreCalculator.compute(expected_bpmn_xml, generated_bpmn_xml)
            LOGGER.info(f"TED score: {ted_score}%")
        except Exception as e:
            LOGGER.error(f"Error calculating TED score: {e}")
            ted_error = f"Error calculating TED score: {str(e)}"

        generated_result = output_data.get("generated_output", "")

        # Always ignore visualization errors
        error_message = None
        if ted_error and not ted_error.startswith("Visualization Error"):
            error_message = ted_error

        # If score is 0 and no explicit error was found, provide a descriptive error
        if ted_score == 0 and not error_message:
            # Add more detailed diagnostic information about the XML differences
            expected_elements = self._count_xml_elements(expected_bpmn_xml_clean)
            generated_elements = self._count_xml_elements(generated_bpmn_xml_clean)

            # Create a detailed comparison message
            error_message = "XML comparison failed with score 0. XML structure differences detected:\n"
            error_message += f"- Expected: {expected_elements} elements\n"
            error_message += f"- Generated: {generated_elements} elements\n"

            # Check for specific structural issues
            if "<bpmn:process" not in generated_bpmn_xml_clean:
                error_message += "- Missing process element in generated XML\n"

            if "<bpmn:definitions" not in generated_bpmn_xml_clean:
                error_message += "- Missing definitions element in generated XML\n"

            if generated_elements == 0:
                error_message += "- Generated XML appears to be empty or malformed\n"

            LOGGER.error(error_message)

        # Ensure added_nodes and removed_nodes are lists (not tuples or other types)
        if not isinstance(added_nodes, list):
            added_nodes = list(added_nodes) if hasattr(added_nodes, "__iter__") else []
        if not isinstance(removed_nodes, list):
            removed_nodes = list(removed_nodes) if hasattr(removed_nodes, "__iter__") else []

        # Create node_stats with safe access to node counts
        node_stats = {
            "expected_count": len(expected_node_ids),
            "generated_count": len(generated_node_ids),
            "common_count": len(common_node_ids),
            "missing_count": len(missing_node_ids),
            "extra_count": len(extra_node_ids),
            "missing_nodes": list(missing_node_ids)[:50] if missing_node_ids else [],
            "extra_nodes": list(extra_node_ids)[:50] if extra_node_ids else [],
        }

        return {
            "eval_score": ted_score,
            "added_elements": added_elements,
            "updated_elements": updated_elements,
            "removed_elements": removed_elements,
            "error": error_message,
            "node_stats": node_stats,
            "generated_result": generated_result,
            "generated_bpmn_xml": generated_bpmn_xml,
            "expected_bpmn_xml": expected_bpmn_xml,
        }

    def _count_xml_elements(self, xml_string: str) -> int:
        if not xml_string:
            return 0

        return xml_string.count("<bpmn:")

    def _extract_element_ids(self, elements: List[Dict[str, Any]]) -> List[str]:
        element_ids = []
        for element in elements:
            if "id" in element:
                element_ids.append(element["id"])
        return element_ids

    def get_threshold(self, prompt_verbosity: str) -> float:
        return ScoreThresholds.TED * 100

    def get_llm_model_details(self) -> Dict[str, Any]:
        model = ModelManager().get_llm_model("bpmn_generation_chat_model_anthropic", ConsumingFeatureType.BPMN_GENERATION)
        return {
            "model_name": getattr(model, "model_name", "N/A"),
            "deployment_name": getattr(model, "deployment_name", "N/A"),
        }


class JSONPatchTEDEvaluatorGeneration(BaseJSONPatchTEDEvaluator):
    evaluation_type = EvalTypes.BPMN_JSON_GENERATION
    override_tool = Tool.EDIT_BPMN

    def load_datasets(self) -> Sequence[BPMNJsonPatchDatasetEntry]:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import BPMNJSONDataset

        datasets = BPMNJSONDataset(self.evaluation_type).load_datasets()
        return cast(Sequence[BPMNJsonPatchDatasetEntry], datasets)


class JSONPatchTEDEvaluatorEdits(BaseJSONPatchTEDEvaluator):
    evaluation_type = EvalTypes.BPMN_JSON_EDITS
    override_tool = Tool.EDIT_BPMN

    def load_datasets(self) -> Sequence[BPMNJsonPatchDatasetEntry]:
        from services.studio._text_to_workflow.bpmn_evaluation.bpmn_evaluation_retrievers import BPMNJSONDataset

        datasets = BPMNJSONDataset(self.evaluation_type).load_datasets()
        return cast(Sequence[BPMNJsonPatchDatasetEntry], datasets)
