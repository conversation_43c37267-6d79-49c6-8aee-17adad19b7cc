# https://huggingface.co/osunlp/UGround-V1-7B
# https://github.com/bytedance/UI-TARS/blob/main/README_coordinates.md
import math
import re
from typing import Dict, Optional

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionUserMessageParam

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import utils

IMAGE_FACTOR = 28
MIN_PIXELS = 100 * 28 * 28
MAX_PIXELS = 16384 * 28 * 28
MAX_RATIO = 200

VIDEO_MIN_PIXELS = 128 * 28 * 28
VIDEO_MAX_PIXELS = 768 * 28 * 28
FRAME_FACTOR = 2
FPS = 2.0
FPS_MIN_FRAMES = 4
FPS_MAX_FRAMES = 768


def round_by_factor(number: float, factor: float) -> int:
    """Returns the closest integer to 'number' that is divisible by 'factor'."""
    return int(round(number / factor) * factor)


def ceil_by_factor(number: float, factor: float) -> int:
    """Returns the smallest integer greater than or equal to 'number' that is divisible by 'factor'."""
    return int(math.ceil(number / factor) * factor)


def floor_by_factor(number: float, factor: float) -> int:
    """Returns the largest integer less than or equal to 'number' that is divisible by 'factor'."""
    return int(math.floor(number / factor) * factor)


def smart_resize(height: int, width: int, factor: int = IMAGE_FACTOR, min_pixels: int = MIN_PIXELS, max_pixels: int = MAX_PIXELS) -> tuple[int, int]:
    """
    Rescales the image so that the following conditions are met:

    1. Both dimensions (height and width) are divisible by 'factor'.

    2. The total number of pixels is within the range ['min_pixels', 'max_pixels'].

    3. The aspect ratio of the image is maintained as closely as possible.
    """
    if max(height, width) / min(height, width) > MAX_RATIO:
        raise ValueError(f"absolute aspect ratio must be smaller than {MAX_RATIO}, got {max(height, width) / min(height, width)}")
    h_bar = max(factor, round_by_factor(height, factor))
    w_bar = max(factor, round_by_factor(width, factor))
    if h_bar * w_bar > max_pixels:
        beta = math.sqrt((height * width) / max_pixels)
        h_bar = floor_by_factor(height / beta, factor)
        w_bar = floor_by_factor(width / beta, factor)
    elif h_bar * w_bar < min_pixels:
        beta = math.sqrt(min_pixels / (height * width))
        h_bar = ceil_by_factor(height * beta, factor)
        w_bar = ceil_by_factor(width * beta, factor)
    return h_bar, w_bar


def get_absolute_coords(normalized_coords, image_base64):
    # normalized_coords: model output coordinates (x, y)
    image = utils.image_base64_to_pil_image(image_base64)
    model_output_width, model_output_height = normalized_coords
    width, height = image.size
    new_height, new_width = smart_resize(height, width)
    new_coordinate = (int(model_output_width / new_width * width), int(model_output_height / new_height * height))
    return new_coordinate


message_template = """You are a GUI agent. You are given a task and your action history, with screenshots. You need to perform the next action to complete the task.

## Output Format
Action: ...

## Action Space
click(start_box='<|box_start|>(x1,y1)<|box_end|>')
drag(start_box='<|box_start|>(x1,y1)<|box_end|>', end_box='<|box_start|>(x3,y3)<|box_end|>')
scroll(start_box='<|box_start|>(x1,y1)<|box_end|>', direction='down or up or right or left') # Show more information on the `direction` side.

## User Instruction
{instruction}
"""


def parse_action(response: str) -> str:
    """response = Action: click(start_box='(x1,y1)'), return  click(start_box='(x1,y1)')"""
    action_match = re.search(r"Action:\s*(.*)", response)
    if action_match:
        action = action_match.group(1).strip()
        return action
    else:
        raise ValueError("Action not found in response")


def extract_action_info(action_line: str) -> Dict[str, Optional[object]]:
    """
    Extracts the action name, start_box, end_box, content, direction, and key from an action string.

    Args:
        action_line (str): The action part (e.g., from 'Action: ...').

    Returns:
        Dict[str, Optional[object]]: Dictionary with keys: action, start_box (tuple), end_box (tuple), content (str), direction (str), key (str)
    """
    result: Dict[str, Optional[object]] = {"action": None, "start_box": None, "end_box": None, "content": None, "direction": None, "key": None}

    # Extract action name
    action_match = re.match(r"^(\w+)\(", action_line)
    if action_match:
        result["action"] = str(action_match.group(1))

    # Extract start_box
    start_box_match = re.search(r"start_box='\(([^,]+),([^\)]+)\)'", action_line)
    if start_box_match:
        result["start_box"] = (int(round(float(start_box_match.group(1)))), int(round(float(start_box_match.group(2)))))

    # Extract end_box (only for drag)
    end_box_match = re.search(r"end_box='\(([^,]+),([^\)]+)\)'", action_line)
    if end_box_match:
        result["end_box"] = (int(end_box_match.group(1)), int(end_box_match.group(2)))

    # Extract content (only for type)
    content_match = re.search(r"content='(.*?)'", action_line, re.DOTALL)
    if content_match:
        result["content"] = content_match.group(1)

    # Extract direction (only for scroll)
    direction_match = re.search(r"direction='(.*?)'", action_line)
    if direction_match:
        result["direction"] = direction_match.group(1)

    # Extract key (only for hotkey)
    key_match = re.search(r"key='(.*?)'", action_line)
    if key_match:
        result["key"] = key_match.group(1)

    return result


class UITARSPredictor(utils.GroundingBaseModel):
    def __init__(self, model_name: str, options: dict):
        self.client = AsyncOpenAI(
            base_url=options["base_url"],
            api_key=options["api_key"],
        )
        # ByteDance-Seed/UI-TARS-1.5-7B
        self.model_name = model_name

    # https://raw.githubusercontent.com/bytedance/UI-TARS/refs/heads/main/data/test_messages.json
    def format_openai_template(self, description: str, image_base64: str) -> list[ChatCompletionUserMessageParam]:
        prompt_text = message_template.format(instruction=description)
        return [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt_text,
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                    },
                ],
            }
        ]

    def parse_response(self, response_text: str):
        """response text should be  ... (x1, y1) ...   with x, y integers"""
        pattern = r"^\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)\s*$"
        match = re.match(pattern, response_text)
        if match:
            x = int(match.group(1))
            y = int(match.group(2))
            return (x, y)
        else:
            console_logger.error("Response format not recognized.")
            return None

    async def predict(self, request: utils.GroundingRequest) -> utils.GroundingOutput:
        image_base64 = request.image_base64
        messages = self.format_openai_template(request.description, image_base64)

        completion = await self.client.chat.completions.create(
            model=self.model_name,
            messages=messages,
            temperature=0.0,  # REMEMBER to set temperature to ZERO!
            # REMEMBER to set temperature to ZERO!
            # REMEMBER to set temperature to ZERO!
        )

        response_content = completion.choices[0].message.content
        if response_content is None:
            raise ValueError("UITARS response is None")
        console_logger.debug("UITARS: response %s", response_content)

        try:
            action_line = parse_action(response_content)
            parsed_action = extract_action_info(action_line)
        except Exception as e:
            print(response_content)
            print("UITARS parse action error:", e)

        if parsed_action["action"] is None:
            raise ValueError("UITARS Agent action is None")

        console_logger.debug("UITARS: parsed action: %s", parsed_action)

        if parsed_action["start_box"] is not None:
            parsed_action["start_box"] = list(get_absolute_coords(parsed_action["start_box"], image_base64))
        else:
            print("UITARS start_box is None", response_content)
        if parsed_action["end_box"] is not None:
            parsed_action["end_box"] = list(get_absolute_coords(parsed_action["end_box"], image_base64))

        # console_logger.debug("UITARS response: %s", response_content)
        # position = self.parse_response(response_content)
        # if position is None:
        #     raise ValueError("UITARS position is None")
        # x, y = position
        # image = utils.image_base64_to_pil_image(image_base64)

        # model_output_width = x
        # model_output_height = y
        # width, height = image.size
        # new_height, new_width = smart_resize(height, width)
        # new_coordinate = (int(model_output_width / new_width * width), int(model_output_height / new_height * height))
        # x, y = new_coordinate
        # console_logger.debug("Coords: %s", (x, y))
        # out = utils.GroundingOutput(description=response_content, bbox=None, position=(x, y))
        out = utils.GroundingOutput(
            description=response_content,
            bbox=None,
            position=parsed_action["start_box"],
            end_position=parsed_action["end_box"],
        )
        return out
