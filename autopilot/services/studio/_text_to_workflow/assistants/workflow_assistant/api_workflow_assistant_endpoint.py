from services.studio._text_to_workflow.api_workflow.api_workflow_endpoint import TASK as API_WORKFLOW_GENERATION_TASK
from services.studio._text_to_workflow.api_workflow.api_workflow_request_schema import APIWorkflowRequest, APIWorkflowResponse
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_endpoint import WorkflowAssistantBaseEndpoint
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_router_task import APIWorkflowRouterTask
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import (
    APIWorkflowAssistantChangeResponse,
    APIWorkflowAssistantRequest,
    WorkflowAssistantRouterResponse,
)
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WorkflowProcessingError, WorkflowProcessingErrorType

GENERATION_STATUS_MESSAGES: dict[str, dict[str, str]] = {
    "generate-workflow": {
        "start": "I'll generate a new workflow based on your request.",
        "success": "I've created a new workflow based on your request.",
        "error": "I was unable to generate a workflow. Please try again.",
    },
    "edit-workflow": {
        "start": "I'll edit the workflow based on your request.",
        "success": "I've edited the workflow based on your request.",
        "error": "I was unable to edit the workflow. Please try again.",
    },
}

# API Workflow Projects only have one file, so we use a fixed path
WORKFLOW_FILE_PATH = "API.json"


class APIWorkflowAssistantEndpoint(WorkflowAssistantBaseEndpoint[APIWorkflowAssistantRequest]):
    def __init__(self):
        self.router_task = APIWorkflowRouterTask()

    async def handle_edit_workflow(
        self,
        router_response: WorkflowAssistantRouterResponse,
        request: APIWorkflowAssistantRequest,
        message_emitter: MessageEmitter,
        emit_message: bool = True,
    ) -> APIWorkflowAssistantChangeResponse:
        return await self._handle_generation_request(router_response, request, message_emitter, "edit-workflow", emit_message)

    async def handle_generate_workflow(
        self,
        router_response: WorkflowAssistantRouterResponse,
        request: APIWorkflowAssistantRequest,
        message_emitter: MessageEmitter,
        emit_message: bool = True,
    ) -> APIWorkflowAssistantChangeResponse:
        return await self._handle_generation_request(router_response, request, message_emitter, "generate-workflow", emit_message)

    async def handle_rewrite_workflow(
        self,
        router_response: WorkflowAssistantRouterResponse,
        request: APIWorkflowAssistantRequest,
        message_emitter: MessageEmitter,
        emit_message: bool = True,
    ) -> APIWorkflowAssistantChangeResponse:
        # for API workflows, we use the generate-workflow scenario to rewrite the workflow,
        # the only difference between generate and rewrite is the fact that existing workflow is not null
        return await self._handle_generation_request(router_response, request, message_emitter, "generate-workflow", emit_message)

    async def _handle_generation_request(
        self,
        router_response: WorkflowAssistantRouterResponse,
        request: APIWorkflowAssistantRequest,
        message_emitter: MessageEmitter,
        scenario: str,
        emit_message: bool = True,
    ) -> APIWorkflowAssistantChangeResponse:
        if emit_message:
            await message_emitter.emit_message(GENERATION_STATUS_MESSAGES[scenario]["start"])

        # Include additional instructions if available
        query = router_response.request
        if router_response.instructions:
            query = f"{router_response.request}\n\nAdditional instructions: {router_response.instructions}"

        # Get the first workflow reference (which should be the one to edit)
        target_workflow = router_response.workflowRefs[0].content if router_response.workflowRefs else None

        # Call workflow generation task
        result: APIWorkflowResponse = await API_WORKFLOW_GENERATION_TASK.generate_workflow(
            APIWorkflowRequest(
                userRequest=query,
                connections=request.availableEntities.connections,  # will probably need connections as well
                expressionLanguage=request.expressionLanguage,
                existingWorkflow=target_workflow,
                runId=request.runId,
                mode="edit" if scenario == "edit-workflow" else "workflow",
            )
        )

        workflow_data = result.get("result") if result else None
        if workflow_data:
            return APIWorkflowAssistantChangeResponse(
                path=WORKFLOW_FILE_PATH,
                scenario=scenario,
                newWorkflowContent=workflow_data,
                previousWorkflowContent="",
                message=GENERATION_STATUS_MESSAGES[scenario]["success"],
                summary=result["summary"],
                errors=result["errors"],
            )

        return APIWorkflowAssistantChangeResponse(
            scenario=scenario,
            path=WORKFLOW_FILE_PATH,
            newWorkflowContent=None,
            previousWorkflowContent=None,
            message=GENERATION_STATUS_MESSAGES[scenario]["error"],
            errors=[
                WorkflowProcessingError(
                    workflow_path=[],
                    type=WorkflowProcessingErrorType.UNKNOWN,
                )
            ],
        )
