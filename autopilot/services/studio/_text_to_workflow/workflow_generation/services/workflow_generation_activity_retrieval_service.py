import asyncio
import itertools
import json
import pathlib
import typing as t
from abc import abstractmethod
from typing import Generic, TypeVar

import langchain_community.callbacks
import numpy as np
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompt_values import Chat<PERSON>romptV<PERSON>ue
from openai import PermissionDeniedError

from services.studio._text_to_workflow.common import constants as studio_constants
from services.studio._text_to_workflow.common.activity_retriever import (
    ActivitiesRetriever,
    ActivitiesRetrieverBase,
)
from services.studio._text_to_workflow.common.connections_loader import (
    get_connections_data,
)
from services.studio._text_to_workflow.common.constants import MANUAL_TRIGGER
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDefinition,
    ActivitySearchOptions,
    ActivityType,
    Connection,
    PlanStep,
    TargetFramework,
)
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.models.output_parsers import (
    MinifiedPydanticOutputParser,
    is_out_of_tokens,
)
from services.studio._text_to_workflow.utils import paths, workflow_utils
from services.studio._text_to_workflow.utils.activity_utils import (
    parse_proposed_activities,
    parse_proposed_activity,
    remove_duplicate_proposed_activities,
)
from services.studio._text_to_workflow.utils.errors import ActivityRetrievalException
from services.studio._text_to_workflow.utils.formatting_utils import escape_braces, format_partially, format_recursively
from services.studio._text_to_workflow.utils.inference.llm_schema import (
    ConsumingFeatureType,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import (
    workflow_generation_retrievers,
    workflow_generation_schema,
)
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import (
    GenerationSettingsBuilder,
)
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import (
    ConnectionEmbeddingsRetriever,
)
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import (
    ActivitiesProposal,
    ActivityRetrievalLimits,
    ActivityRetrievalParams,
    ActivityRetrievalResult,
    ActivityRetrievalWorkflowDetails,
    GenerationSettings,
    RawActivityRetrievalResult,
)
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval_details import (
    ActivityRetrievalDetails,
)
from services.studio._text_to_workflow.workflow_generation.services.helpers.ignored_activities_helper import (
    get_ignored_activities_map,
    get_ignored_namespaces,
)
from services.studio._text_to_workflow.workflow_generation.services.helpers.relevant_activities_finder import (
    expand_with_related_activities,
)
from services.studio._text_to_workflow.workflow_generation.services.helpers.wf_gen_constants import (
    CONTROL_FLOW_STATEMENTS,
    IGNORED_ACTIVITIES,
    IGNORED_TRIGGERS,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_helpers import (
    get_workflow_activities,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityRetrievalGeneration,
    ActivityRetrievalGenerationBase,
    ProposedActivity,
    WfGenDataPointV2,
)

LOGGER = AppInsightsLogger()

ActivitiesRetrieverType = TypeVar("ActivitiesRetrieverType", bound=ActivitiesRetrieverBase)

PLAN_ACTIVITIES_UPPER_LIMIT = 8
PLAN_ACTIVITIES_LOWER_LIMIT = 4
EXTRA_MAX_TOKENS_LIMIT = 750
EXTRA_THINKING_TOKENS_LIMIT = 2000
MAX_TOKENS_LIMIT_FOR_FOR_RETRY = 8192


class WorkflowGenerationActivityRetrievalServiceBase(Generic[ActivitiesRetrieverType]):
    prompt_config: dict[str, t.Any]
    common_system_msg_parts: dict[str, t.Any]
    activities_retriever: ActivitiesRetrieverType
    post_processing_config: dict[str, t.Any]
    parser: PydanticOutputParser

    def __init__(
        self,
        connection_embeddings_retriever: ConnectionEmbeddingsRetriever,
        activities_retriever: ActivitiesRetrieverType,
    ):
        self.retry_count = 3
        self.connection_embeddings_retriever = connection_embeddings_retriever

        self.embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")

        self.activities_retriever = activities_retriever

        common_system_msg_parts_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "activity_retrieval_format_prompt.yaml"

        self.common_system_msg_parts = yaml_load(common_system_msg_parts_path)

    def _embed_workflow_steps(self, steps: list[str]) -> list[PlanStep]:
        step_embeddings = self.embedding_model.encode_batch(steps, batch_size=64, instruction_set="icl", instruction_type="query")
        return [PlanStep(text=step, embedding=step_embedding, triggers=[], activities=[]) for step, step_embedding in zip(steps, step_embeddings, strict=False)]

    @staticmethod
    def _get_generation_activities_by_priority(
        proposal: ActivitiesProposal,
        max_no_activities: int,
        query_priority_limit: int | None = None,
        workflow_priority_limit: int | None = None,
        triggerMode: ActivityType = "activity",
    ) -> list[ProposedActivity]:
        """Extracts a single list of activities we should send directly to the LLM model"""
        query_activities: list[list[ProposedActivity]] = proposal.query_proposal_activities if triggerMode == "activity" else proposal.query_proposal_triggers
        workflow_activities = proposal.workflow_proposal_activities if triggerMode == "activity" else proposal.workflow_proposal_triggers

        query_activities = query_activities if query_priority_limit is None else query_activities[:query_priority_limit]
        workflow_activities = workflow_activities if workflow_priority_limit is None else workflow_activities[:workflow_priority_limit]

        # we will alternate activities from both lists when joining them, this prevents trimming relevant activities
        result = [act for act in itertools.chain.from_iterable(itertools.zip_longest(query_activities, workflow_activities))]
        result = remove_duplicate_proposed_activities([act for act_set in query_activities + workflow_activities for act in act_set])

        # trim to max no of elements allowed
        return result[:max_no_activities]

    def _get_activity_details_for_query(
        self,
        query_steps: list[PlanStep],
        params: ActivityRetrievalParams,
        is_multistep_query: bool,
    ) -> ActivityRetrievalDetails:
        limits = ActivityRetrievalLimits(
            activities_limit=self.prompt_config["proposed_activities_selection"]["query_activities_limit"],
            triggers_limit=self.prompt_config["proposed_activities_selection"]["query_triggers_limit"],
            main_query_activities_percentage=self.prompt_config["proposed_activities_selection"]["main_query_activities_percentage"],
        )
        regular_result = self._get_activity_details_for_query_with_limits(query_steps, params, limits, is_multistep_query, False)

        # Get generic activities only if allowed
        generic_result = None
        if params.allow_generic_activities:
            limits = ActivityRetrievalLimits(
                activities_limit=self.prompt_config["proposed_activities_selection"]["query_generic_activities_limit"],
                triggers_limit=self.prompt_config["proposed_activities_selection"]["query_triggers_limit"],
                main_query_activities_percentage=self.prompt_config["proposed_activities_selection"]["main_query_activities_percentage"],
            )
            generic_result = self._get_activity_details_for_query_with_limits(query_steps, params, limits, is_multistep_query, True)

        return self._merge_activity_retrieval_results(regular_result, generic_result)

    def _merge_activity_retrieval_results(
        self,
        regular_result: ActivityRetrievalDetails,
        generic_result: ActivityRetrievalDetails | None,
    ) -> ActivityRetrievalDetails:
        """
        Merges two ActivityRetrievalDetails objects by combining all their lists.
        """
        if generic_result is None:
            return regular_result

        merged_basic_activities = [
            activity
            for regular, generic in itertools.zip_longest(regular_result.basic_activities, generic_result.basic_activities)
            for activity in [regular, generic]
            if activity is not None
        ]
        merged_basic_triggers = [
            trigger
            for regular, generic in itertools.zip_longest(regular_result.basic_triggers, generic_result.basic_triggers)
            for trigger in [regular, generic]
            if trigger is not None
        ]

        merged_relevant_triggers = [
            trigger
            for regular, generic in itertools.zip_longest(regular_result.relevant_triggers, generic_result.relevant_triggers)
            for trigger in [regular, generic]
            if trigger is not None
        ]

        merged_ground_truth_activities = regular_result.ground_truth_activities + generic_result.ground_truth_activities
        merged_ground_truth_triggers = regular_result.ground_truth_triggers + generic_result.ground_truth_triggers

        merged_activity_steps = [
            regular + generic for regular, generic in itertools.zip_longest(regular_result.activity_steps, generic_result.activity_steps, fillvalue=[])
        ]

        return ActivityRetrievalDetails(
            basic_activities=merged_basic_activities,
            basic_triggers=merged_basic_triggers,
            relevant_triggers=merged_relevant_triggers,
            activity_steps=merged_activity_steps,
            ground_truth_activities=merged_ground_truth_activities,
            ground_truth_triggers=merged_ground_truth_triggers,
        )

    def _get_activity_details_for_query_with_limits(
        self,
        query_steps: list[PlanStep],
        params: ActivityRetrievalParams,
        limits: ActivityRetrievalLimits,
        is_multistep_query: bool,
        generic_activities: bool,
    ) -> ActivityRetrievalDetails:
        """
        Extracts relevant activities and triggers from a given query.
        It receives the query embeddings and returns the relevant activities and triggers.
        """

        # For multi-step queries, we want 70% of activities from the main query (first embedding)
        # and 30% from individual steps
        if is_multistep_query:
            # Main query (70% of activities)
            main_query_limit = int(limits.activities_limit * limits.main_query_activities_percentage / 100)

            # Individual steps (30% of activities distributed among steps)
            steps_count = len(query_steps) - 1  # Exclude the main query
            individual_steps_limit = max(
                1,
                int(limits.activities_limit * (1 - (limits.main_query_activities_percentage / 100)) / steps_count),
            )

            # Get activities for the main query (first embedding)
            main_query_steps, basic_triggers, basic_activities, _ = self.activities_retriever.get_relevant(
                query_steps[:1],
                params.connections,
                ActivitySearchOptions(
                    mode=params.mode if params.mode != "workflow" else "sequence",
                    target_framework=params.target_framework,
                    ignored_namespaces=params.ignored_namespaces,
                    ignored_activities=params.ignored_activities,
                    generic_activities=generic_activities,
                ),
                main_query_limit,
            )

            # Get activities for individual steps
            individual_steps, _, _, _ = self.activities_retriever.get_relevant(
                query_steps[1:],
                params.connections,
                ActivitySearchOptions(
                    mode="sequence",
                    target_framework=params.target_framework,
                    ignored_namespaces=params.ignored_namespaces,
                    ignored_activities=params.ignored_activities,
                    generic_activities=generic_activities,
                ),
                individual_steps_limit,
            )

            # Combine all steps
            steps = main_query_steps + individual_steps
        else:
            # For single-step queries, use the existing approach
            steps, basic_triggers, basic_activities, _ = self.activities_retriever.get_relevant(
                query_steps,
                params.connections,
                ActivitySearchOptions(
                    mode="sequence",
                    target_framework=params.target_framework,
                    ignored_namespaces=params.ignored_namespaces,
                    ignored_activities=params.ignored_activities,
                    generic_activities=generic_activities,
                ),
                limits.activities_limit,
            )

        # 2. We get the relevant triggers for the query (if there is a trigger)
        # get triggers from whole query, not from first step
        # on sequence mode, only extract triggers if current acitivy is first in the given workflow
        relevant_triggers = self.activities_retriever.search(
            query_steps[0],
            params.connections,
            ActivitySearchOptions(
                mode=None,
                target_framework=params.target_framework,
                ignored_namespaces=params.ignored_namespaces,
                ignored_activities=params.ignored_activities,
                generic_activities=generic_activities,
            ),
            "trigger",
            limits.triggers_limit,
        )

        return ActivityRetrievalDetails(
            basic_triggers=parse_proposed_activities(basic_triggers),
            basic_activities=parse_proposed_activities(basic_activities),
            relevant_triggers=parse_proposed_activities(relevant_triggers),
            activity_steps=[parse_proposed_activities(step["activities"]) for step in steps],
            ground_truth_activities=[],
            ground_truth_triggers=[],
        )

    def _get_activity_retrieval_generation(
        self, act_retrieval_generation: ActivityRetrievalGenerationBase | ActivityRetrievalGeneration
    ) -> ActivityRetrievalGeneration:
        if isinstance(act_retrieval_generation, ActivityRetrievalGeneration):
            return act_retrieval_generation
        else:
            return ActivityRetrievalGeneration(
                plan=act_retrieval_generation.plan,
                ambiguities=act_retrieval_generation.ambiguities,
                score=act_retrieval_generation.score,
                activities=act_retrieval_generation.activities,
                inexistentActivities=act_retrieval_generation.inexistentActivities,
                undocumentedHttpRequests=act_retrieval_generation.undocumentedHttpRequests,
                inexistentTriggers=[],
                triggers=[],
            )

    def get_parser(self, use_triggers: bool) -> PydanticOutputParser:
        if use_triggers:
            return MinifiedPydanticOutputParser(pydantic_object=ActivityRetrievalGeneration)
        else:
            return MinifiedPydanticOutputParser(pydantic_object=ActivityRetrievalGenerationBase)

    async def _get_model_suggestions(
        self,
        query: str,
        proposed_triggers: list[ProposedActivity],
        proposed_activities: list[ProposedActivity],
        existing_workflow_namespaces: set[str] | None,
        demonstrations: list[tuple[str, str]],  # list of (query, plan)
        params: ActivityRetrievalParams,
        target_framework: TargetFramework,
        current_workflow: str = "",
    ) -> tuple[ActivityRetrievalGeneration, str, str] | None:
        # store the data used for each particular data point
        plan_examples = []
        for demo in demonstrations:
            # Get demo query/plan either from ApiWorkflowDataPoint or WfGenDataPointV2
            demo_query = demo[0]
            demo_plan = demo[1]

            if demo_plan and demo_query:
                plan_examples.append(
                    format_partially(self.prompt_config["demonstration_template"], {"query": json.dumps(demo_query), "plan": json.dumps(demo_plan)})
                )

        # For long queries, we need to increase the max tokens
        if len(query) > 100:
            extra_output_tokens = min(EXTRA_MAX_TOKENS_LIMIT, int(len(query) - 100) / 2)  # Add 1 token for each 2 characters over 100 for the output
            extra_thinking_tokens = min(EXTRA_THINKING_TOKENS_LIMIT, int(len(query) - 100) * 2)  # Add 2 tokens for each character over 100 for the thinking

            # total max models tokens includes both thinking and output, so to the total we should add both values
            params.model.max_model_tokens = int(params.model.max_model_tokens + extra_output_tokens + extra_thinking_tokens)  # type: ignore
            params.model.max_thinking_tokens = int(params.model.max_thinking_tokens + extra_thinking_tokens)  # type: ignore

        # If there no proposed triggers, we ask the model to generate the retrieval generation base without triggers, simplifying the response
        parser = self.get_parser(len(proposed_triggers) > 0)

        # Create a mapping of activities and triggers to unique ids so that the ids are single tokens in the model
        # Otherwise, OpenAI GPT4o class models may improperly hallucinate repeating sequences of the same id in P99 cases.
        activities_dict: dict[int, ProposedActivity] = {i + 1: act for i, act in enumerate(proposed_activities)}
        triggers_dict: dict[int, ProposedActivity] = (
            {i + len(activities_dict) + 1: act for i, act in enumerate(proposed_triggers)} if params.mode == "workflow" else {}
        )

        system_prompt_values = (
            self.common_system_msg_parts
            | self.prompt_config
            | {
                "plan_examples": escape_braces("\n".join(plan_examples)),
                "format_instructions": escape_braces(parser.get_format_instructions()),
                "full_activities_definition": escape_braces(self._format_activity_definitions(activities_dict)) or "no activities available",
                "full_triggers_definition": escape_braces(self._format_activity_definitions(triggers_dict)) or "no triggers available",
                "current_workflow_namespaces": escape_braces("".join([f"\n  - {namespace}" for namespace in existing_workflow_namespaces or []])),
                "current_workflow": escape_braces(current_workflow),
            }
        )
        if not existing_workflow_namespaces:  # for API workflows, eliminate common placeholder
            system_prompt_values["system_msg_current_workflow_namespaces"] = ""
        if not current_workflow:  # empty out certain sections of the prompt if there is no current workflow
            system_prompt_values["system_msg_current_workflow"] = ""
            system_prompt_values["system_msg_current_workflow_namespaces"] = ""

        gen_messages: list[BaseMessage] = [
            SystemMessage(content=format_recursively(self.prompt_config["system_msg"], system_prompt_values)),
            HumanMessage(content=format_recursively(self.prompt_config["user_msg_template"], {"query": query})),
        ]

        raw_response = None
        for i in range(self.retry_count):
            try:
                # we get the response as a string, as we also want to have it available for eval and logging purposes
                chat_prompt_object = ChatPromptValue(messages=gen_messages)
                prompt = chat_prompt_object.to_string()
                raw_response = await params.model.ainvoke(chat_prompt_object)

                if not isinstance(raw_response.content, str):
                    raise ActivityRetrievalException(f"Expected string content from LLM response, got {type(raw_response.content)}")

                # parse the response
                act_retrieval_generation_base: ActivityRetrievalGenerationBase = parser.parse(raw_response.content)

                act_retrieval_generation: ActivityRetrievalGeneration = self._get_activity_retrieval_generation(act_retrieval_generation_base)

                # model might've accidentally added a trigger to the activities list and vice versa, we will account for this by merging the lists
                retrievals: list[int] = act_retrieval_generation.activities + act_retrieval_generation.triggers

                # Re-map back to the original activity ids - any hallucinated ids will be removed
                act_retrieval_generation.activities = list([activities_dict[activity_id]["id"] for activity_id in retrievals if activity_id in activities_dict])
                act_retrieval_generation.triggers = list([triggers_dict[trigger_id]["id"] for trigger_id in retrievals if trigger_id in triggers_dict])

                return act_retrieval_generation, raw_response.content, prompt
            except Exception as e:
                if hasattr(e, "status_code") and e.status_code == 401 or isinstance(e, PermissionDeniedError):
                    raise e

                if is_out_of_tokens(e, raw_response, params.model.max_model_tokens, ActivityRetrievalGeneration):  # type: ignore
                    params.model.max_model_tokens = min(params.model.max_model_tokens * 2, MAX_TOKENS_LIMIT_FOR_FOR_RETRY)  # type: ignore

                LOGGER.exception(f"⚠️ Activity retrieval failed with: {e}.")
                if i == self.retry_count - 1:
                    raise e
                continue

    @abstractmethod
    def _format_activity_definitions(self, activities: dict[int, ProposedActivity]) -> str:
        raise NotImplementedError

    def _get_activity_details_for_workflow(
        self,
        activity_retrieval_details: ActivityRetrievalWorkflowDetails,
        activity_steps: list[PlanStep],
        params: ActivityRetrievalParams,
    ) -> ActivityRetrievalDetails:
        limit = (
            self.prompt_config["proposed_activities_selection"]["wf_step_activities_limit_for_edit"]
            if params.edit_current_workflow
            else self.prompt_config["proposed_activities_selection"]["wf_step_activites_limit"]
        )
        regular_result = self._get_activity_details_for_workflow_with_limits(activity_retrieval_details, activity_steps, params, limit, False)

        # Get generic activities only if allowed
        generic_result = None
        if params.allow_generic_activities:
            limit = (
                self.prompt_config["proposed_activities_selection"]["wf_step_generic_activities_limit_for_edit"]
                if params.edit_current_workflow
                else self.prompt_config["proposed_activities_selection"]["wf_step_generic_activities_limit"]
            )
            generic_result = self._get_activity_details_for_workflow_with_limits(activity_retrieval_details, activity_steps, params, limit, True)

        return self._merge_activity_retrieval_results(regular_result, generic_result)

    def _get_activity_details_for_workflow_with_limits(
        self,
        activity_retrieval_details: ActivityRetrievalWorkflowDetails,
        activity_steps: list[PlanStep],
        params: ActivityRetrievalParams,
        activities_limit_per_step: int,
        generic_activities: bool,
    ) -> ActivityRetrievalDetails:
        step_thoughts, _, _, _ = self.activities_retriever.get_relevant(
            activity_steps,
            params.connections,
            ActivitySearchOptions(
                mode=params.mode,
                target_framework=params.target_framework,
                ignored_namespaces=params.ignored_namespaces,
                ignored_activities=params.ignored_activities,
                generic_activities=generic_activities,
            ),
            activities_limit_per_step,
        )
        # get all the activities from the thoughts
        activity_thoughts = step_thoughts[1:] if activity_retrieval_details.has_trigger else step_thoughts

        return ActivityRetrievalDetails(
            basic_activities=[],
            basic_triggers=[],
            relevant_triggers=parse_proposed_activities(step_thoughts[0]["triggers"]) if activity_retrieval_details.has_trigger else [],
            activity_steps=[parse_proposed_activities(step["activities"]) for step in activity_thoughts],
            ground_truth_activities=parse_proposed_activities(activity_retrieval_details.ground_truth_activities),
            ground_truth_triggers=[parse_proposed_activity(activity_retrieval_details.ground_truth_trigger)]
            if activity_retrieval_details.ground_truth_trigger is not None
            else [],
        )

    def _get_proposed_activities_from_plan_and_llm_proposals(
        self,
        retrievalGeneration: ActivityRetrievalGeneration,
        params: ActivityRetrievalParams,
    ) -> t.Tuple[set[str], set[str]]:
        """Extracts relevant activities and triggers from a plan and a list of anticipated but fictive activities."""

        # Skip trying to fetch activities from plan parts that contain control flow statements
        plan_parts = retrievalGeneration.plan.split("\n") if retrievalGeneration.plan else []
        # for workflows always include first step, we will extract triggers from it
        fetchable_plan_parts = [
            plan_part
            for i, plan_part in enumerate(plan_parts)
            if (params.mode == "workflow" and i == 0) or not any(statement in plan_part for statement in CONTROL_FLOW_STATEMENTS)
        ]

        # get anticipated triggers and activities from llm output
        inexistent_activities = retrievalGeneration.inexistentActivities if retrievalGeneration.inexistentActivities is not None else []
        inexistent_triggers = retrievalGeneration.inexistentTriggers if retrievalGeneration.inexistentTriggers is not None else []

        # we will embed all the plan parts, activities and triggers in a single call, for efficiency
        embeddings = self._embed_workflow_steps(fetchable_plan_parts + inexistent_triggers + inexistent_activities)

        if len(embeddings) == 0:
            return set(), set()

        # TODO: Add multi-search to the activities retriever - this should make this much much faster
        # TODO: We should not do this for the first step in the plan if it's a trigger
        # TODO: Keep a single embedding context for the entire workflow generation and pre-compute the query embedding and the connections embeddings together
        # Inference on the plan is 2.25s vs 1.5s. Acceptable as we shave off time from the query embedding and retrieve less DAP activities uselessly.
        # we will extract the activities from the plan parts

        # Extract the closest activities for each plan part and keep the closest ones
        plan_activities: dict[str, ActivityDefinition] = {}
        for part_embedding in embeddings[: len(fetchable_plan_parts)]:
            activities = self.activities_retriever.search(
                part_embedding,
                params.connections,
                ActivitySearchOptions(
                    mode=None,
                    target_framework=params.target_framework,
                    ignored_namespaces=params.ignored_namespaces,
                    ignored_activities=params.ignored_activities,
                    generic_activities=False,
                ),
                "activity",
                self.post_processing_config["plan_step_activities"],
            )
            for a in activities:
                if a["fullClassName"] not in plan_activities:
                    plan_activities[a["fullClassName"]] = a
                elif a["similarity"] is not None and plan_activities[a["fullClassName"]]["similarity"] is not None:
                    # only replace the activity if the new similarity score is higher
                    existing_similarity = plan_activities[a["fullClassName"]]["similarity"]
                    assert existing_similarity is not None
                    if a["similarity"] > existing_similarity:
                        plan_activities[a["fullClassName"]] = a

        # we want to take the first 2 thirds of activities sorted by similarity, but bounded by LOWER/UPPER LIMITS
        plan_act_limit = int(len(fetchable_plan_parts) * 2 / 3)
        plan_act_limit = max(min(plan_act_limit, PLAN_ACTIVITIES_UPPER_LIMIT), PLAN_ACTIVITIES_LOWER_LIMIT)
        sorted_plan_activities = sorted(plan_activities.values(), key=lambda x: x["similarity"] or 0.0, reverse=True)
        extracted_activities = set([a["fullClassName"] for a in sorted_plan_activities[:plan_act_limit]])
        extracted_triggers: set[str] = set()

        # extract the activities from the anticipated_inexistent_activities
        for act_embedding in embeddings[len(fetchable_plan_parts) :]:
            activities = self.activities_retriever.search(
                act_embedding,
                params.connections,
                ActivitySearchOptions(
                    mode=None,
                    target_framework=params.target_framework,
                    ignored_namespaces=params.ignored_namespaces,
                    ignored_activities=params.ignored_activities,
                    generic_activities=False,
                ),
                "activity",
                self.post_processing_config["anticipated_inexistent_activities"],
            )
            extracted_activities |= set([a["fullClassName"] for a in activities])

        # extract the triggers from the anticipated_inexistent_activities
        for trigger_embedding in embeddings[len(fetchable_plan_parts) + len(inexistent_activities) :]:
            triggers = self.activities_retriever.search(
                trigger_embedding,
                params.connections,
                ActivitySearchOptions(
                    mode=None,
                    target_framework=params.target_framework,
                    ignored_namespaces=params.ignored_namespaces,
                    ignored_activities=params.ignored_activities,
                    generic_activities=False,
                ),
                "trigger",
                self.post_processing_config["anticipated_inexistent_triggers"],
            )
            extracted_triggers |= set([t["fullClassName"] for t in triggers])

        if params.mode == "workflow":
            # get triggers for the first step of the plan
            plan_triggers = self.activities_retriever.search(
                embeddings[0],
                params.connections,
                ActivitySearchOptions(
                    mode=None,
                    target_framework=params.target_framework,
                    ignored_namespaces=params.ignored_namespaces,
                    ignored_activities=params.ignored_activities,
                    generic_activities=False,
                ),
                "trigger",
                self.post_processing_config["plan_step_triggers"],
            )
            extracted_triggers |= set([t["fullClassName"] for t in plan_triggers])

        return extracted_triggers - set(IGNORED_TRIGGERS), extracted_activities - set(IGNORED_ACTIVITIES)

    def _prepare_generate_relevant_activities(
        self,
        query: str,
        params: ActivityRetrievalParams,
        workflow_details: ActivityRetrievalWorkflowDetails | None,
    ) -> tuple[
        ActivitiesProposal,
        np.ndarray,
        np.ndarray,
        dict[str, Connection],
        set[str] | None,
    ]:
        # extract the query steps for embedding
        query_steps = workflow_utils.get_display_names(query)
        # Filter out empty query steps or steps that trim to empty
        query_steps = [step for step in query_steps if step and step.strip()]
        # if the query has multiple steps, we will embed them individually, but the entire query too
        is_multistep_query = len(query_steps) > 1
        query_elements = [query] + query_steps if is_multistep_query else [query]

        # we will embed the query and the workflow in a single call, for efficiency
        embeddings = self._embed_workflow_steps(query_elements + (workflow_details.embedding_steps if workflow_details else []))

        query_activities_details = self._get_activity_details_for_query(embeddings[: len(query_elements)], params, is_multistep_query)

        activities_proposal = ActivitiesProposal(
            query_proposal_activities=query_activities_details.organize_activities_by_priority(),
            query_proposal_triggers=query_activities_details.organize_triggers_by_priority(),
            workflow_proposal_activities=[],
            workflow_proposal_triggers=[],
        )

        existing_workflow_namespaces = None
        if workflow_details:
            # if we extracted any embeddings from the workflow, we will process them and extract the activities and triggers
            wf_activities_details = self._get_activity_details_for_workflow(workflow_details, embeddings[len(query_steps) :], params)
            activities_proposal.workflow_proposal_activities = wf_activities_details.organize_activities_by_priority()
            activities_proposal.workflow_proposal_triggers = wf_activities_details.organize_triggers_by_priority()
            if any(wf_activities_details.ground_truth_activities) or any(wf_activities_details.ground_truth_triggers):
                existing_workflow_namespaces = set([a["namespace"] for a in wf_activities_details.ground_truth_activities]) | set(
                    [a["namespace"] for a in wf_activities_details.ground_truth_triggers]
                )

        query_embedding = np.array(embeddings[0]["embedding"])
        connections_embedding, connections_by_key = self.connection_embeddings_retriever.get_connections_embedding(params.connections, query_embedding)

        return (
            activities_proposal,
            query_embedding,
            connections_embedding,
            connections_by_key,
            existing_workflow_namespaces,
        )

    async def _generate_relevant_activities(
        self,
        query: str,
        demonstrations: list[tuple[str, str]],  # list of (query, plan)
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        params: ActivityRetrievalParams,
        activities_proposal: ActivitiesProposal,
        existing_workflow_namespaces: set[str] | None,
        target_framework: TargetFramework,
        connections_by_key: dict[str, Connection],
        max_no_activities: int,
        current_workflow: str = "",
    ) -> RawActivityRetrievalResult:
        proposed_activities = self._get_generation_activities_by_priority(activities_proposal, max_no_activities=max_no_activities)
        proposed_triggers = (
            self._get_generation_activities_by_priority(activities_proposal, max_no_activities=max_no_activities, triggerMode="trigger")
            if params.mode == "workflow"
            else []
        )

        with langchain_community.callbacks.get_openai_callback() as cb:
            model_result = await self._get_model_suggestions(
                query,
                proposed_triggers,
                proposed_activities,
                existing_workflow_namespaces,
                demonstrations,
                params,
                target_framework,
                current_workflow,
            )
            if model_result is None:
                raise Exception("Failed to get model suggestions")
            result, raw_response, prompt = model_result

            usage = workflow_generation_schema.TokenUsage(
                model=params.model.model_name,
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )

        # TODO: Slightly inefficient, improve
        raw_retrieved_activities = [a["type_full_name"] for a in proposed_activities if a["id"] in result.activities]
        raw_retrieved_triggers = list({t["type_full_name"] for t in proposed_triggers if t["id"] in result.triggers})

        expanded_with_related_activities = expand_with_related_activities(set(raw_retrieved_activities), target_framework)
        extended_triggers, extended_activities = self._get_proposed_activities_from_plan_and_llm_proposals(result, params)

        return RawActivityRetrievalResult(
            generation=result,
            prompt=prompt,
            token_usage=usage,
            proposed_triggers=proposed_triggers,
            proposed_activities=proposed_activities,
            retrieved_triggers=list(set(raw_retrieved_triggers) | extended_triggers),
            retrieved_activities=list(expanded_with_related_activities | extended_activities),
            ignored_activities=params.ignored_activities,
            unprocessed_retrieved_triggers=raw_retrieved_triggers,
            unprocessed_retrieved_activities=raw_retrieved_activities,
            generation_details=activities_proposal,
            query_embedding=query_embedding,
            connections_embedding=connections_embedding,
            connections_by_key=connections_by_key,
            raw_response=raw_response,
        )


class WorkflowGenerationActivityRetrievalService(WorkflowGenerationActivityRetrievalServiceBase[ActivitiesRetriever]):
    def __init__(
        self,
        activities_retriever: ActivitiesRetriever,
        connection_embeddings_retriever: ConnectionEmbeddingsRetriever,
        prompt_builder_component: WorkflowGenerationPromptBuilderComponent,
    ):
        self.prompt_builder_component = prompt_builder_component
        super().__init__(connection_embeddings_retriever, activities_retriever)

        # TO DO: this should be a parameter
        config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "activity_retrieval_prompt.yaml"
        self.config: dict[str, t.Any] = yaml_load(config_path)

        self.post_processing_config = self.config["post_processing"]
        self.prompt_config = self.config["prompt"]
        self.demonstrations_config = self.config["demonstration_ranking"]

        self.demo_retriever = workflow_generation_retrievers.DemonstrationsRetriever(
            # TO DO: pass the config object here, instead of the path
            config_path.as_posix(),
            paths.get_wf_gen_activity_retriever_dataset_path(),
            paths.get_wf_gen_activity_retriever_path(),
            activities_retriever,
        )

        self.ignored_activities = get_ignored_activities_map(activities_retriever)

    def _format_activity_definitions(self, activities: dict[int, ProposedActivity]) -> str:
        return "\n".join(
            [
                json.dumps({"id": activity_id, "description": activity["description"], "display_name": activity["display_name"]})
                for activity_id, activity in activities.items()
            ]
        )

    async def generate_relevant_activities(
        self,
        query: str,
        workflow: t.Optional[Workflow],
        connections: list[Connection],
        mode: ActivitiesGenerationMode,
        target_framework: TargetFramework,
        settings: GenerationSettings,
        eval_mode: bool = False,
    ) -> ActivityRetrievalResult:
        model = ModelManager().get_llm_model(
            settings.model_options["retrieval"],
            ConsumingFeatureType.WORKFLOW_GENERATION,
        )

        params: ActivityRetrievalParams = ActivityRetrievalParams(
            target_framework=target_framework,
            mode=mode,
            connections=connections,
            ignored_namespaces=get_ignored_namespaces(workflow, self.activities_retriever, target_framework, settings),
            ignored_activities=self.ignored_activities[target_framework],
            eval_mode=eval_mode,
            # since sequence will essentially generate a new sequence, we don't need to consider it a workflow edit operation (it's a generation operation)
            edit_current_workflow=workflow is not None and mode != "sequence",
            model=model,
            allow_generic_activities=False,
        )
        workflow_details = self._parse_workflow(workflow, target_framework) if workflow else None
        (
            activities_proposal,
            query_embedding,
            connections_embedding,
            connections_by_key,
            existing_workflow_namespaces,
        ) = self._prepare_generate_relevant_activities(query, params, workflow_details)

        demonstrations = self._get_relevant_demonstrations(query, query_embedding, connections_embedding, params)
        demo_query_details = [(demo["query"], demo["plan"]) for demo in demonstrations]

        # we will include the current workflow in the prompt if it's not a sequence generation
        if params.edit_current_workflow:
            assert workflow is not None
            serialized_workflow = workflow.lmyaml(
                use_dap_activity_name=True,
                include_name=False,
                include_arguments=False,
                include_variables=False,
                include_packages=False,
                include_namespaces=False,
            )
            current_workflow = self.prompt_builder_component.prune_existing_workflow(serialized_workflow, model, 10000)
        else:
            current_workflow = ""

        result = await self._generate_relevant_activities(
            query,
            demo_query_details,
            query_embedding,
            connections_embedding,
            params,
            activities_proposal,
            existing_workflow_namespaces,
            target_framework,
            connections_by_key,
            self.prompt_config["proposed_activities_selection"]["max_no_activities"],
            current_workflow=current_workflow,
        )

        return ActivityRetrievalResult._from_internal_result(result, demonstrations)

    def _parse_workflow(self, workflow: Workflow, target_framework: TargetFramework) -> ActivityRetrievalWorkflowDetails:
        """Get activities and triggers from a given workflow, validate their existence, and extract their display names for embedding."""

        has_trigger = False
        ground_truth_trigger: ActivityDefinition | None = None

        steps_to_embed: list[str] = []

        workflow_details = get_workflow_activities(workflow)
        existing_activities = list(workflow_details["activities"].values())

        if workflow_details["trigger"]:
            steps_to_embed = [workflow_details["trigger"].display_name]

            ground_truth_trigger = self.activities_retriever.get(workflow_details["trigger"].activity_id, target_framework)
            if ground_truth_trigger is None:
                print(f"⚠️ Trigger {workflow_details['trigger'].activity_id} not found")

        # we build a pseudo-plan with the thought of each retrieved activity
        for activity in existing_activities:
            if activity.display_name != studio_constants.SEQUENCE_GENERATION_INSERTION_PHRASE:
                steps_to_embed.append(activity.display_name)

        # We ensure that we have all the retrieved activities for the workflow representing the ground truth
        ground_truth_activities: list[ActivityDefinition] = []
        for activity in existing_activities:
            activity_definition = self.activities_retriever.get(activity.activity_id, target_framework)
            if activity_definition is not None:
                ground_truth_activities.append(activity_definition)
            else:
                print(f"⚠️ Activity {activity.activity_id} not found")  # Warning

        return ActivityRetrievalWorkflowDetails(has_trigger, ground_truth_trigger, ground_truth_activities, steps_to_embed)

    def _get_relevant_demonstrations(
        self,
        query: str,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        params: ActivityRetrievalParams,
    ) -> list[WfGenDataPointV2]:
        demonstrations = self.demo_retriever.get_relevant(
            query,
            query_embedding,
            connections_embedding,
            {
                "static": 1,
                "uia": 2 if params.mode == "testcase" else 1,
                "testcase": 1 if params.mode == "testcase" else 0,
            },
            params.target_framework,
            params.ignored_namespaces,
            set(),
            params.ignored_activities,
        )

        demonstration_details: list[WfGenDataPointV2] = [WfGenDataPointV2(**demo) for demos in demonstrations.values() for demo in demos]

        # if we're doing an eval, we don't want to include the current query in the demonstrations
        if params.eval_mode:
            demonstration_details = [demo for demo in demonstration_details if demo["query"] != query]

        demos_needed = self.prompt_config["demonstration_filtering"]["planing_demonstrations_limit"]
        wf_min_trigger_count = self.prompt_config["demonstration_filtering"]["wf_min_trigger_count"]

        if params.mode != "workflow":
            return demonstration_details[:demos_needed]

        # for workflow generation - we need to ensure at least some demonstrations have non-manual triggers
        trigger_demo_indexes = [i for i, d in enumerate(demonstration_details) if len(d["used_triggers"]) > 0 and d["used_triggers"][0] != MANUAL_TRIGGER]
        demo_indexes = set(trigger_demo_indexes[:wf_min_trigger_count])  # ensure we keep the most relevant n demos with triggers

        # fill the remaining demos
        for i in range(len(demonstration_details)):
            if len(demo_indexes) >= demos_needed:
                break
            demo_indexes.add(i)

        return [demonstration_details[i] for i in sorted(demo_indexes)]


if __name__ == "__main__":
    # test_prompt = "when an invoice is created in an application, download the file, then check with document understanding if the vendor ID and amount match the purchase order. If they match, approve for payment, else create a human task"
    # test_prompt = "when an account is created in Salesforce, add a new task in ServiceNow"
    test_prompt = "Create a zoom meeting, create a new lead on salesforce and send me a slack reminder with the content 'New zoom meeting scheduled'"

    activitiesFetcher = ActivitiesRetriever()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activitiesFetcher, embedding_model)
    prompt_builder_component = WorkflowGenerationPromptBuilderComponent(activitiesFetcher)
    activitiesRetrievalService = WorkflowGenerationActivityRetrievalService(activitiesFetcher, connection_embeddings_retriever, prompt_builder_component)

    default_connections = get_connections_data()
    generation_settings = GenerationSettingsBuilder.build_generation_settings(test_prompt, "Windows", None, [], [], [])

    result = asyncio.run(
        activitiesRetrievalService.generate_relevant_activities(
            test_prompt,
            None,
            default_connections,
            "workflow",
            "Windows",
            generation_settings,
            True,
        )
    )
    print(result.retrieved_activities)
    print(result.retrieved_triggers)
