from dataclasses import dataclass

import langchain_core
import langchain_core.language_models
import numpy as np

from services.studio._text_to_workflow.common.api_workflow.schema import ApiWorkflowDataPoint
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDefinition,
    Connection,
    TargetFramework,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.request_schema import ModelOptions
from services.studio._text_to_workflow.workflow_generation.config.constants import (
    ExcelPackageOption,
    MailPackageOption,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityRetrievalGeneration,
    ProposedActivity,
    WfGenDataPointV2,
)


class GenerationSettings:
    """Contains general user configurations that should be applied during the generation"""

    def __init__(
        self,
        model_options: dict[str, ModelOptions] | None,
        default_excel_package: ExcelPackageOption | None,
        default_mail_package: MailPackageOption | None,
    ):
        self.default_excel_package: ExcelPackageOption | None = default_excel_package
        self.default_mail_package: MailPackageOption | None = default_mail_package
        if model_options is None:
            model_options = {
                "retrieval": {"model_name": "activity_retrieval_gemini_model"},
                "generation": {"model_name": "workflow_generation_gemini_model"},
            }

        self.model_options: dict[str, ModelOptions] = model_options


@dataclass
class ActivityRetrievalParams:
    """Collection of common parameters used in the activity retrieval process"""

    target_framework: TargetFramework
    mode: ActivitiesGenerationMode | None
    eval_mode: bool
    connections: list[Connection]
    ignored_namespaces: set[str]
    ignored_activities: set[str]
    edit_current_workflow: bool  # if True, the current workflow will be edited according to the query (we want to distinguish between edit and sequence mode)
    model: langchain_core.language_models.BaseChatModel
    allow_generic_activities: bool


@dataclass
class ActivityRetrievalLimits:
    main_query_activities_percentage: int
    activities_limit: int
    triggers_limit: int


class ActivitiesProposal:
    """Contains the activities/triggers proposed to implement the automation described by a query
    Each proposal contains of a list of lists, the activities are grouped in inner lists ordered by their relevance.
    If 2 activities belong to the same group, they are equally relevant.
    """

    def __init__(
        self,
        query_proposal_activities: list[list[ProposedActivity]],
        workflow_proposal_activities: list[list[ProposedActivity]],
        query_proposal_triggers: list[list[ProposedActivity]],
        workflow_proposal_triggers: list[list[ProposedActivity]],
    ):
        # proposed activities/triggers derived from query embeddings
        self.query_proposal_activities = query_proposal_activities
        self.query_proposal_triggers = query_proposal_triggers
        # proposed activities/triggers derived from the existing workflow embeddings
        self.workflow_proposal_activities = workflow_proposal_activities
        self.workflow_proposal_triggers = workflow_proposal_triggers


@dataclass
class RawActivityRetrievalResult:
    generation: ActivityRetrievalGeneration
    prompt: str
    token_usage: TokenUsage
    proposed_triggers: list[ProposedActivity]
    proposed_activities: list[ProposedActivity]
    retrieved_triggers: list[str]
    retrieved_activities: list[str]
    ignored_activities: set[str]
    unprocessed_retrieved_activities: list[str]
    unprocessed_retrieved_triggers: list[str]
    generation_details: ActivitiesProposal
    query_embedding: np.ndarray
    connections_embedding: np.ndarray
    connections_by_key: dict[str, Connection]
    raw_response: str


@dataclass
class ActivityRetrievalResult:
    generation: ActivityRetrievalGeneration
    prompt: str
    raw_response: str
    token_usage: TokenUsage
    demonstrations: list[WfGenDataPointV2]
    proposed_triggers: list[ProposedActivity]
    proposed_activities: list[ProposedActivity]
    retrieved_triggers: list[str]
    retrieved_activities: list[str]
    ignored_activities: set[str]
    unprocessed_retrieved_triggers: list[str]
    unprocessed_retrieved_activities: list[str]
    generation_details: ActivitiesProposal
    query_embedding: np.ndarray
    connections_embedding: np.ndarray
    connections_by_key: dict[str, Connection]

    @classmethod
    def _from_internal_result(
        cls,
        internal_result: RawActivityRetrievalResult,
        demonstrations: list[WfGenDataPointV2],
    ) -> "ActivityRetrievalResult":
        return cls(
            generation=internal_result.generation,
            prompt=internal_result.prompt,
            token_usage=internal_result.token_usage,
            demonstrations=demonstrations,
            proposed_triggers=internal_result.proposed_triggers,
            proposed_activities=internal_result.proposed_activities,
            retrieved_triggers=internal_result.retrieved_triggers,
            retrieved_activities=internal_result.retrieved_activities,
            ignored_activities=internal_result.ignored_activities,
            unprocessed_retrieved_triggers=internal_result.unprocessed_retrieved_triggers,
            unprocessed_retrieved_activities=internal_result.unprocessed_retrieved_activities,
            generation_details=internal_result.generation_details,
            query_embedding=internal_result.query_embedding,
            connections_embedding=internal_result.connections_embedding,
            connections_by_key=internal_result.connections_by_key,
            raw_response=internal_result.raw_response,
        )


@dataclass
class APIActivityRetrievalResult:
    generation: ActivityRetrievalGeneration
    prompt: str
    raw_response: str
    token_usage: TokenUsage
    demonstrations: list[ApiWorkflowDataPoint]
    proposed_activities: list[ProposedActivity]
    retrieved_activities: list[str]
    ignored_activities: set[str]
    unprocessed_retrieved_activities: list[str]
    generation_details: ActivitiesProposal
    query_embedding: np.ndarray
    connections_embedding: np.ndarray
    connections_by_key: dict[str, Connection]

    @classmethod
    def _from_internal_result(
        cls,
        internal_result: RawActivityRetrievalResult,
        demonstrations: list[ApiWorkflowDataPoint],
    ) -> "APIActivityRetrievalResult":
        return cls(
            generation=internal_result.generation,
            prompt=internal_result.prompt,
            token_usage=internal_result.token_usage,
            demonstrations=demonstrations,
            proposed_activities=internal_result.proposed_activities,
            retrieved_activities=internal_result.retrieved_activities,
            ignored_activities=internal_result.ignored_activities,
            unprocessed_retrieved_activities=internal_result.unprocessed_retrieved_activities,
            generation_details=internal_result.generation_details,
            query_embedding=internal_result.query_embedding,
            connections_embedding=internal_result.connections_embedding,
            connections_by_key=internal_result.connections_by_key,
            raw_response=internal_result.raw_response,
        )


@dataclass
class ActivityRetrievalWorkflowDetails:
    has_trigger: bool
    ground_truth_trigger: ActivityDefinition | None
    ground_truth_activities: list[ActivityDefinition]
    embedding_steps: list[str]
