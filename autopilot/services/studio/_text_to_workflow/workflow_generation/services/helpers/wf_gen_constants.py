from services.studio._text_to_workflow.common import constants

# These will be added programmatically to the list of activities used to build a draft, so they should be ignored when evaluating the retrieval results
IGNORED_ACTIVITIES = {
    "UiPath.Core.Activities.InvokeCode",
    "UiPath.Database.Activities.DatabaseTransaction",
    "System.Activities.Statements.If",
    "System.Activities.Statements.Sequence",
    "UiPath.Core.Activities.ForEach",
    "UiPath.Core.Activities.Assign",
    "UiPath.Core.Activities.Break",
    "UiPath.Core.Activities.Continue",
    "UiPath.Core.Activities.LogMessage",
}

IGNORED_TRIGGERS = {constants.MANUAL_TRIGGER}

CONTROL_FLOW_STATEMENTS = [
    " If ",
    " Else ",
    " ElseIf ",
    " For Each ",
    " While ",
    " Do While ",
    " Switch ",
    " Case ",
    " Break ",
    " Continue ",
    " Try ",
    " Catch ",
    " Finally ",
    " Throw ",
    " Log ",
]
