demonstration_ranking:
  planning_demonstrations_limit: 30
  demonstrations_limit: 4
  force_windows_demonstrations: 2
  embedding_retrieval_docs: 30
  reranking_enable: true
  reranking_docs: 30
  mmr_enable: true
  mmr_similarities: iou_retrieved_activities # choices: embeddings, reranking, pled, plan_tree_edit_distance, iou_activities, iou_retrieved_activities
  mmr_similarities_action: build # choices: build, load, disable; only used at dataset build time
  mmr_docs: 30
  mmr_diversity: 1.0 # 1.0 if you use reranking, 0.2 if not
  sorting_order: descending # choices: ascending, descending

post_processing:
  plan_step_activities: 1 # how many activities to try extract for each plan step
  plan_step_triggers: 1
  anticipated_inexistent_activities: 2 # how many matches for each anticipated inexistent activities to keep
  anticipated_inexistent_triggers: 2

prompt:
  demonstration_filtering:
    planing_demonstrations_limit: 9
    wf_min_trigger_count: 2 # for workflow generation mode, we want at least 2 demonstrations with triggers

  proposed_activities_selection:
    main_query_activities_percentage: 70 # the percentage of activities that should be retrieved from the whole query embedding, the rest will be retrieved from each line of the query embeddings
    query_triggers_limit: 15 # how many related triggers do we want to extract for the query
    query_activities_limit: 200 # how many related activities do we want to extract for the query
    query_generic_activities_limit: 0  # how many generic activities do we want to extract for the query
    wf_step_activites_limit: 2 # how many related activities do we want to extract for each activity in the existing workflow
    wf_step_generic_activities_limit: 0  # how many generic activities do we want to extract for each activity in the existing workflow
    wf_step_activities_limit_for_edit: 10 # how many related activities do we want to extract for each activity when rewriting or editing the existing workflow
    wf_step_generic_activities_limit_for_edit: 0  # how many generic activities do we want to extract for each activity when rewriting or editing the existing workflow
    max_no_activities: 200 # how many activities do we want to extract in total
  prompt_entity_types: activities and triggers

  system_msg: |-
    {intro}
    You will be provided with a JSON list with all the available activities used to build UiPath workflows. The activities and triggers have ids, display names and descriptions. They will appear as JSON objects.
    Multiple activities can be nested inside another activity, forming a tree.
    The user will provide a query along a subset of activity and trigger ids out of the full list that might be used to build an automation that solves the query.

    There are two types of automation:
    - Automations which include a trigger, which means that the workflow will be started when a certain event occurs
    - Automations which do not have a trigger and are started manually.

    {output_format}
    You must only output JSON, do not output any other text.
    Your task is to output a valid JSON with the following properties:
    ```json
    {{"plan":"plan","ambiguities":"ambiguities","score":"query-score","triggers":["triggerId1","triggerId2",...],"activities":["activityId1","activityId2",...],"inexistentActivities":["inexistentActivityName1","inexistentActivityName2",...],"inexistentTriggers":["inexistentTriggerName1","inexistentTriggerName2",...]}}
    ```
    where:
    plan: The plan to solve the goal stated in the query. Retrieve the activities and triggers that are relevant to the plan.
    ambiguities: What are the ambiguities in the query? What is missing?
    queryScore: A score between -1 and 100, representing how much sense the query makes for an automation and whether it has many ambiguities. -1 is used when the query cannot even be represented as an automation.
    triggers: The ids of the relevant triggers out of the ones provided in the # Full List of Triggers that could be used to decide when the workflow should be run. The values in this list should be unique.
    activities: The ids of the relevant activities out of the ones provided in the # Full List of Activities that could be used to implement the automation described in the query. The values in this list should be unique.
    inexistentActivities: Based on the plan and query, as well as what activities you retrieved, please list fictional activity type names that you would have liked to have retrieved, but which do not exist. If you think you did a good job, this should be empty.
    inexistentTriggers: Based on the plan and query, as well as what triggers you retrieved, please list fictional trigger type names that you would have liked to have retrieved, but which do not exist. If you think you did a good job, this should be empty.

    {general_requirements}

    # Relevant Activities and Trigger Requirements:
    {activity_selection_guidelines_1}
    - The triggers should be a list of integers, containing the ""id"" values of the triggers out of the full list of triggers that could be used to launch the automation described in the query. Do not use ""id"" values from the full list of activities for the list of relevant triggers.
    - Attention: If you already added an activity id or a trigger id, you must not add it again.
    {activity_selection_guidelines_2}
    - Make sure we have all the necessary activities to integrate with any 3rd party applications or files mentioned or hinted at in the query.
    - We need to be more exhaustive for activity categories that provide a multitude of activities to achieve the same result. In such cases, we need to include all potentially useful activities. This applies to email, database and excel activities.
    - Our priority should be on eliminating fully irrelevant activities, any potentially relevant activities from the user's list should be included in the relevant activities/triggers lists.
    - When retrieving any activities related to Excel or CSVs, or any sort of data manipulation, make sure to also include the DataTable activities, too (AddRow, AddColumn, AddRange, etc.) so you can manipulate the data into Excel.
    - When asked to "generate something from natural language or using an LLM", make sure to include Content Generation or Chat Completion activities as well.
    - When asked to "create something", make sure to include iterator activities and write/send/invite activities for your type of entity because you will probably be asked to fill in the details into what you created.
    {activity_types}
    - When asked to perform an "HTTP request", make sure to include the "HttpClient" activity (as well as any service-specific activity for HTTP requests, if applicable). You must also return all JSON deserialization activities.
    - When asked to "extract something" (e.g. extract data from a PDF document, CSV or text file), make sure to include the ExtractDocumentDataWithDocumentData, for your type of entity, iterator activities and multiple types of extract activities that seem relevant. You must also include activities for manipulating data table activities (e.g. GenerateDataTable, BuildDataTable, InsertDataTable, AddRow, AddColumn).
    - When asked to "save something", make sure to include the "Save" activity for that type of entity, if available. If unsure, include the multiple "Save" activities (e.g. if you are asked to save a file, include both "SaveFile", "SaveFileAs" and "ExportFileAs").
    - When asked to "click something", "type into something", "get text from something" or "select something that refers to something coming from the UI", you will have to retrieve the "UiPath.UIAutomationNext.Activities.NApplicationCard" activity, which can solve anything related to UI automation.
    - When asked to "verify something", make sure to include multiple types of Verify activities.

    - When getting activities for a trigger plan step that mentions a specific vendor (e.g. OneDrive for "When file uploaded in OneDrive"), make sure to include the OneDrive new file trigger activity. If the query does not mention a vendor but implies a trigger, e.g. "Every time I get a new email", you must include the new email trigger activity from multiple vendors.
    - When I mention something about an invoice or another type of text document that is normally a .docx or .pdf (e.g. contract, etc.), make sure to always include the ExtractDocumentDataWithDocumentData activity.
    - Don't be stupid. Example: You are provided with GSuite activities and Microsoft download activities. If I mention that I need e.g. GSuite to download something, you must include the GSuite download activity, not the Microsoft download activity. Only add both if you are unsure when there is ambiguity about where to download the emails from.
    - If you fail to provide activities and triggers that may be necessary for the plan, you will be heavily penalized.

    - How to get related activities:
      - If the service used by the activities is specified (e.g. Google Drive, Excel, etc.), you must only include activities from that provider. Don't include e.g. Smartsheet activities if the user clearly specified that they want to use Google Sheets.
      - If the service used by the activities is not specified, then you may include related activities from any provider, but try to get the same ones for each (e.g. if you get "UiPath.OneDrive.Activities.GetFile", also get "UiPath.GoogleDrive.Activities.GetFile").
    {activity_list_structuring}
      - Do not blindly increment the id of the last activity or trigger in the list if the next activity is not useful for solving the user query, e.g. this is completely wrong: [0,1,2,3,4,5,6,7,...,100].
      - IMPORTANT: The response activity and trigger lists must only contain the ids of the proposed activities. DO NOT ADD THE ENTIRE ACTIVITY OR TRIGGER DEFINITIONS.
    {ambiguities_requirements}

    {score_requirements}

    # Inexistent Trigger and Activity Type Name Requirements:
    - The inexistentTriggers should be a list with at most 2 elements of fictional trigger type names and inexistentActivities should be a list of fictional activity type names that you would have liked to have retrieved, but which do not exist.
    - However, if you think you did a good job and fulfilled the activities or triggers for the plan well, either one or both of inexistentTriggers and inexistentActivities should be empty.
    - The inexistentTriggers and inexistentActivities should be lists of strings.
    - Example: 
      ```
        Query: "Extract the company email from an invoice PDF document, connect to a database, and find the company id in the database by email."
        Retrieval Critique: "I found an activity for validating a PDF document and two for extracting text or images, but I did not find any activities related to extracting data from a PDF document (and I need to extract the data from the PDF document, which is an invoice), nor did I find one to query a database (at least not one from the Database package, though I did find some from the DataServices package, they should probably not be used together)."
        Anticipated But Inexistent Activity Type Names: ["UiPath.PDF.Activities.ExtractData", "UiPath.Database.Activities.ExecuteQuery", "UiPath.PDF.Activities.ExtractInvoiceData"]
      ```
    - Example 2:
      ```
        Query: "Download the email from Gmail and upload it to Google Drive and OneDrive."
        Retrieval Critique: "I found the OneDrive upload activity, but I did not find the Google Drive upload activity."
        Anticipated But Inexistent Activity Type Names: ["UiPath.GoogleDrive.Activities.UploadFilesConnections"]
      ```

    # Plan Requirements:
    - If the query implies a trigger, its first line should be a trigger. The trigger specifies when the workflow should be run. After the trigger just generate pseudocode that completes the task.
    - It's important to include "If", "While" and "For each" steps where necessary in the pseudocode.
    {plan_requirements}
      Query: "Open the Word document at C:\\example.docx and append the provided text with a new line."
      Bad Example: "1. Open the Word document at C:\\example.docx.\n2. Append the provided text with a new line."
      Good Example: "1. Open the Word document at C:\\example.docx.\n2. Append the provided text with a new line in Word."
    - On a plan with a trigger (e.g. "When employee is created in Workday"), in the next step after the trigger, you should download, get or retrieve the entity the trigger is about if the trigger only provides an id but not the entity itself. Ensure that you include the proper relevant triggers from the list of trigger ids.
    - When the query is imperative and does not specify a trigger, e.g. "Go through the last 100 emails and summarize them", do not attempt to use a trigger, but rather iterate through the emails.
    - For code involving UI automation, write a single step that summarizes all the actions to be performed on a webpage, e.g. Navigate to URL and do something. However, if we need to perform something and then Get the value of an element on the screen, you must write two steps. 
      - You should never blindly add a plan step for opening a website, page or browser but without doing anything on it in that plan step, e.g. "Open the website https://amazon.com", or "Open the page https://amazon.com", or "Open the browser" or "Open the web page" or "Open the web browser" or "Open the web browser and navigate to https://amazon.com". All these plan steps are wrong and should never be added.
    - If there are explicit statements regarding UI automation in the query, it's important to include all of them in the plan.

    {footer}

    {footer_triggers_definition}

  system_msg_current_workflow_namespaces: |
    - The current workflow has the following namespaces: {current_workflow_namespaces}
    - When planning and retrieving activities, make sure to relate the query to the current workflow, for which you have the namespaces.
    - Make sure to try to relate the query to the current workflow namespaces when writing the plan. Example: you have an ambiguous query saying "Download the email attachments" and not mentioning a provider, while the current workflow namespaces only have UiPath.Mail.Outlook.Activities. It is clear that the user wants to download the email attachments from Outlook, so you should not include Gmail activities, and you should mention "Outlook" in the plan.

  user_msg_template: |-
    QUERY:
    {query}

  demonstration_template: |-
    ```
    QUERY: {query}
    PLAN: {plan}
    ```
