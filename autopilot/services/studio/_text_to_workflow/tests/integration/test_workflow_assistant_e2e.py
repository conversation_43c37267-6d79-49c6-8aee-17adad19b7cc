import json
import os
from typing import Any

import pytest

from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_endpoint import WorkflowAssistantEndpoint
from services.studio._text_to_workflow.assistants.workflow_assistant.workflow_assistant_schema import (
    RPAWorkflowAssistantRequest,
    WorkflowAssistantChangeResponse,
)
from services.studio._text_to_workflow.common.prompt_utils import append_debug_runid_to_prompt
from services.studio._text_to_workflow.common.walkers import ActivitiesAndTriggersCollector
from services.studio._text_to_workflow.tests.utils.testable_message_emitter import TestableMessageEmitter
from services.studio._text_to_workflow.utils.workflow_utils import load_workflow_instance

WORKFLOW_ASSISTANT = WorkflowAssistantEndpoint()


@pytest.mark.asyncio
async def test_generate_workflow_validation(run_id: str | None = None):
    """Test with a simple prompt that we can properly generate a workflow for document processing."""
    test_data = load_fixture("assistant_generate_workflow.json")

    # The additional prompt for the router is needed to prevent caching.
    if run_id:
        test_data["request"]["userRequest"] = append_debug_runid_to_prompt(test_data["request"]["userRequest"], run_id)
    response, _ = await run_scenario(test_data, run_id)

    # check the response workflow is written to Main.xaml, since the existing workflow is empty
    assert response.path == "Main.xaml", "Expected path to be Main.xaml"

    assert response.scenario == "generate-workflow", f"Expected scenario to be 'generate-workflow', got {response.scenario}"
    assert response.newWorkflowContent is not None

    # Parse the generated workflow
    workflow = load_workflow_instance("", response.newWorkflowContent)

    # Collect activities from the generated workflow
    collector = ActivitiesAndTriggersCollector()
    collected = collector.collect(workflow)

    # Assert that we have at least some activities generated
    assert len(collected["activity"]) > 0, "Expected at least one activity in the generated workflow"

    # Verify the relevant activities are present
    activity_ids = [a.activity_id for a in collected["activity"]]
    assert "UiPath.IntelligentOCR.StudioWeb.Activities.PDF.ExtractPDFText.ExtractPDFText" in activity_ids
    assert "UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections" in activity_ids


def load_fixture(filename: str) -> dict[str, Any]:
    """Load a workflow fixture from the fixtures directory"""
    fixtures_dir = os.path.join(os.path.dirname(__file__), "..", "fixtures", "workflow_assistant_fixtures")
    fixture_path = os.path.join(fixtures_dir, filename)
    with open(fixture_path, "r") as f:
        return json.load(f)


async def run_scenario(test_data: dict, run_id: str | None = None) -> tuple[WorkflowAssistantChangeResponse, TestableMessageEmitter]:
    """Test the workflow assistant for a specific scenario with test data"""
    # Create a request from the test data
    request_data = test_data["request"]

    # Create a request instance
    request = RPAWorkflowAssistantRequest(**request_data)
    if run_id:
        request.runId = run_id

    message_emitter = TestableMessageEmitter()

    # Process the request
    response: WorkflowAssistantChangeResponse = await WORKFLOW_ASSISTANT.perform_assistant_request(request, message_emitter)

    return response, message_emitter
