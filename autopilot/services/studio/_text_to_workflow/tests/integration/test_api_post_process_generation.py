import pytest
from langchain_core.language_models import BaseChatModel

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import (
    ConnectorIntegrationWithMetadata,
    PostGenApiWorkflow,
    PostGenForEach,
    PostGenIf,
)
from services.studio._text_to_workflow.common.api_workflow.schema import (
    SEQUENCE_ACTIVITY_TYPE,
    ApiWorkflow,
    ConnectorIntegration,
    ForEach,
    HttpRequest,
    JsInvoke,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.tests.integration.utils.dynamic_activities_mocks import (
    load_raw_api_workflow_datapoint,
    mock_dynamic_activities_config_map,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import InvalidExpressionError

_api_postprocessing_fixtures_path = conftest._fixtures_path / "api_postprocessing_fixtures"


@pytest.fixture
def component(api_activities_retriever: APIActivitiesRetriever) -> ApiWfPostGenerationProcessingService:
    """Create the API workflow post-processing component for testing."""
    return ApiWfPostGenerationProcessingService(api_activities_retriever)


@pytest.fixture
def config_map() -> dict[str, dict]:
    return mock_dynamic_activities_config_map()


@pytest.fixture
def model() -> BaseChatModel:
    return ModelManager().get_llm_model("workflow_draft_generation_gemini_model", ConsumingFeatureType.WORKFLOW_GENERATION)


@pytest.mark.asyncio
async def test_post_gen_workflow_is_created_correctly(component: ApiWfPostGenerationProcessingService, config_map: dict[str, dict], model: BaseChatModel):
    """Test the process_raw_workflow method with the CancelLatestTimeOffRequest demo workflow."""
    # Get the specific workflow we want to test
    raw_workflow = load_raw_api_workflow_datapoint("CancelLatestTimeOffRequest")
    # Call the method being tested
    result = await component.process_raw_workflow(raw_workflow, model, config_map, "js", {}, [], can_augment_configmap=False)

    # Verify the result
    assert result is not None
    assert isinstance(result.processed_workflow, PostGenApiWorkflow)

    # Check the structure of activities in the processed workflow
    root = result.processed_workflow.root
    assert root.activity == SEQUENCE_ACTIVITY_TYPE
    assert len(root.do) == 2

    # Check the GET Time-off Requests activity
    first_activity = root.do[0]
    assert isinstance(first_activity, ConnectorIntegrationWithMetadata)
    assert first_activity.activity == "BambooHRGet_Time_off_Requests"

    # Check exact metadata values for first activity
    assert first_activity.metadata.method == "GET"
    assert first_activity.metadata.configuration == "dummyConfig"
    assert first_activity.metadata.uiPathActivityTypeId == "468d10a7-5d93-3cca-98fc-a319dda2b118"

    # Check connection ID
    assert first_activity.with_.connectionId == "40c2218d-cba4-44bf-a134-5c0cde0ce432"

    # Check actual query parameters
    assert "start" in first_activity.with_.queryParameters
    assert "end" in first_activity.with_.queryParameters
    assert "employeeId" in first_activity.with_.queryParameters

    # Check the Change Time-off Request Status activity
    second_activity = root.do[1]
    assert isinstance(second_activity, ConnectorIntegrationWithMetadata)
    assert second_activity.activity == "BambooHRChange_Time_off_Request_Status"

    # Check exact metadata values for second activity
    assert second_activity.metadata.method == "PUT"
    assert second_activity.metadata.uiPathActivityTypeId == "93d7da58-c901-3e60-be55-14860918bafd"
    assert second_activity.metadata.configuration == "dummyConfig"

    # Check connection ID
    assert second_activity.with_.connectionId == "40c2218d-cba4-44bf-a134-5c0cde0ce432"

    # Check actual body parameters
    assert "id" in second_activity.with_.pathParameters
    assert "start_date" in second_activity.with_.bodyParameters
    assert "end_date" in second_activity.with_.bodyParameters
    assert "status" in second_activity.with_.bodyParameters


@pytest.mark.asyncio
async def test_post_gen_handles_hallucinated_activities_correctly(
    component: ApiWfPostGenerationProcessingService, config_map: dict[str, dict], model: BaseChatModel
):
    """Test the process_raw_workflow method with the hallucinated activity workflow."""
    # Get the specific workflow we want to test
    with open(_api_postprocessing_fixtures_path / "hallucinated_activity_wf.txt", "r") as f:
        raw_workflow = f.read()

    result = await component.process_raw_workflow(raw_workflow, None, config_map, "js", {}, [])

    # Verify the result
    assert result.processed_workflow is not None

    do_block = result.processed_workflow.root.do

    # hallucinated activity should be replaced with a http request
    assert isinstance(do_block[0], HttpRequest)

    # other activities should keep the same type
    assert isinstance(do_block[1], JsInvoke)
    assert isinstance(do_block[2], ConnectorIntegrationWithMetadata)
    assert isinstance(do_block[3], PostGenForEach)
    # expressions referencing hallucinated activities are either deleted or set to empty string
    assert do_block[1].code == ""
    params = do_block[2].with_.bodyParameters | do_block[2].with_.pathParameters | do_block[2].with_.queryParameters
    assert params["id"] == ""
    assert params["start_date"] == ""
    assert params["end_date"] == ""
    assert do_block[3].for_.in_ == ""

    # other properties should be kept
    assert params["status"] == '${"denied"}'
    assert params["note"] == '${"Cancelled by automation"}'
    assert do_block[3].for_.each == "ticket"
    assert do_block[3].for_.at == "index"


# TEMPORARY: remove after SW will support customizable export properties
@pytest.mark.asyncio
async def test_post_gen_replaces_ids_with__object_name_correctly(
    component: ApiWfPostGenerationProcessingService, config_map: dict[str, dict], model: BaseChatModel
):
    """Test the process_raw_workflow method with the hallucinated activity workflow."""
    # Get the specific workflow we want to test
    with open(_api_postprocessing_fixtures_path / "activity_id_replacing_wf.txt", "r") as f:
        raw_workflow = f.read()

    result = await component.process_raw_workflow(raw_workflow, None, config_map, "js", {}, [])

    # Verify the result
    assert result is not None
    assert isinstance(result.processed_workflow, PostGenApiWorkflow)

    root = result.processed_workflow.root.do
    # Check ids are replaced with object name
    assert root[0].id == "Freshdesk_HTTP_Request_1"
    assert root[1].id == "normalised-teams_channels_messages-v2_1"
    assert isinstance(root[2], PostGenForEach)
    assert root[2].id == "For_Each_1"
    assert root[2].do[0].id == "tasks_1"
    assert root[3].id == "curated_contact_1"
    assert root[4].id == "curated_contact_2"
    assert root[5].id == "send_message_to_user_v2_1"

    # Check that the expressions referencing the activities are changed to reflect the new ids
    assert isinstance(root[1], ConnectorIntegrationWithMetadata)
    assert root[1].with_.bodyParameters["body.content"] == "${$context.outputs.Freshdesk_HTTP_Request_1.content.filter}"
    assert isinstance(root[2].do[0], ConnectorIntegrationWithMetadata)
    assert root[2].do[0].with_.bodyParameters["notes"] == '${$context.outputs["normalised-teams_channels_messages-v2_1"].content.text}'
    assert isinstance(root[5], ConnectorIntegrationWithMetadata)
    assert root[5].with_.bodyParameters["messageToSend"] == "${$context.outputs.curated_contact_2.content.FirstName}"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ["test_file", "proposed_activities", "expected_workflow_activities"],
    [
        # check that a workflow with an invalid activity type is parsed correctly
        ("invalid_activity_type_wf.txt", [], []),
        # check that an if with no then is parsed correctly
        (
            "invalid_for_activity_wf.txt",
            [
                "UiPath.IntegrationService.Activities.Runtime.Activities.FreshdeskSearch_Tickets",
                "UiPath.IntegrationService.Activities.Runtime.Activities.FreshdeskUpdate_Ticket",
            ],
            ["FreshdeskSearch_Tickets", "ForEach"],
        ),
    ],
)
async def test_post_gen_fixes_invalid_yaml(
    component: ApiWfPostGenerationProcessingService,
    config_map: dict[str, dict],
    model: BaseChatModel,
    test_file: str,
    proposed_activities: list[str],
    expected_workflow_activities: list[str],
):
    """Test the API YAML parser with the given test file and assert that the parsing works."""
    # Get the test YAML content
    invalid_wf_path = _api_postprocessing_fixtures_path / test_file

    with open(invalid_wf_path, "r") as f:
        raw_workflow = f.read()

    # # build the proposed activities
    activities = [act for act in (component.activity_retriever.get_by_class_name(a, "activity") for a in proposed_activities) if act is not None]

    result = await component.process_raw_workflow(
        raw_workflow, model, config_map, "js", {}, activities, is_model_retry_permitted=False, can_augment_configmap=False
    )

    # Simple verification that parsing worked
    assert result is not None
    assert isinstance(result.processed_workflow, PostGenApiWorkflow)

    # check that the expected activities are present in the output workflow
    for i, expected_activity in enumerate(expected_workflow_activities):
        assert expected_activity in result.processed_workflow.root.do[i].activity


@pytest.mark.asyncio
async def test_post_gen_handles_invalid_expressions_with_dap_output_types_inside(
    component: ApiWfPostGenerationProcessingService, config_map: dict[str, dict], model: BaseChatModel
):
    """Test the process_raw_workflow method with the hallucinated activity workflow."""
    # Get the specific workflow we want to test
    with open(_api_postprocessing_fixtures_path / "invalid_expression_with_dap_output_type.txt", "r") as f:
        raw_workflow = f.read()

    result = await component.process_raw_workflow(raw_workflow, None, config_map, "js", {}, [], is_model_retry_permitted=False)

    assert result is not None
    assert isinstance(result.processed_workflow, PostGenApiWorkflow)
    root = result.processed_workflow.root.do

    # check that the invalid expression is removed
    assert isinstance(root[1], PostGenIf)
    assert root[1].condition == ""

    # check that Invalid Expression Error is correctly added to the result
    invalid_expression_error = next(e for e in result.post_generation_errors if isinstance(e, InvalidExpressionError))
    assert invalid_expression_error.error.startswith("Invalid reference to output type")
    assert "getContactByEmail_Retrieve" in invalid_expression_error.error


@pytest.mark.asyncio
async def test_post_gen_handles_literal_expressions_correctly(
    component: ApiWfPostGenerationProcessingService, config_map: dict[str, dict], model: BaseChatModel
):
    """Test the process_raw_workflow method with the hallucinated activity workflow."""
    # Get the specific workflow we want to test
    with open(_api_postprocessing_fixtures_path / "invalid_literal_expression_wf.txt", "r") as f:
        raw_workflow = f.read()

    result = await component.process_raw_workflow(raw_workflow, None, config_map, "js", {}, [])

    assert result is not None

    assert isinstance(result.generated_workflow, ApiWorkflow)
    root = result.generated_workflow.root.do

    assert isinstance(root[0], ConnectorIntegration)
    assert isinstance(root[0].with_, dict)
    assert root[0].with_.get("method", "") == '${"GET"}'

    assert isinstance(root[2], ConnectorIntegration)
    assert isinstance(root[2].with_, dict)
    assert root[2].with_.get("Subject", "") == '${"Oracle Netsuite Campaigns Data"}'
    assert root[2].with_.get("To", "") == "${$workflow.input.EmailAddress}"
    assert root[2].with_.get("SaveAsDraft", "") == "${false}"
    assert root[2].with_.get("TestInt", "") == "${0}"
    assert root[2].with_.get("TestDouble", "") == "${1.4}"


@pytest.mark.asyncio
async def test_post_gen_handles_non_escapable_properties_correctly(
    component: ApiWfPostGenerationProcessingService, config_map: dict[str, dict], model: BaseChatModel
):
    """Test the process_raw_workflow method with the non escapable properties activity workflow."""
    # Get the specific workflow we want to test
    with open(_api_postprocessing_fixtures_path / "non_escapable_properties_wf.txt", "r") as f:
        raw_workflow = f.read()

    result = await component.process_raw_workflow(raw_workflow, None, config_map, "js", {}, [])

    assert result is not None
    assert isinstance(result.generated_workflow, ApiWorkflow)

    root = result.generated_workflow.root.do

    assert isinstance(root[1], ForEach)
    assert root[1].for_.at == "currentItemIndex"
    assert root[1].for_.each == "currentItem"
    assert root[1].for_.in_ == "$context.outputs.search_sub_tickets_1.content"

    assert isinstance(root[1].do[0], HttpRequest)
    assert root[1].do[0].with_.method == "GET"
    assert (
        root[1].do[0].with_.endpoint
        == "${`https://asana.createtask.org/?project=${$workflow.input.AsanaProject.toString()}&wkspate=${$workflow.input.AsanaWorkSpace.toString()}`}"
    )

    assert isinstance(root[1].do[1], JsInvoke)
    assert not root[1].do[1].code.startswith("${")


@pytest.mark.asyncio
async def test_post_gen_handles_input_arguments_set_to_empty_object_correctly(
    component: ApiWfPostGenerationProcessingService, config_map: dict[str, dict], model: BaseChatModel
):
    """Test the process_raw_workflow method with the non escapable properties activity workflow."""
    # Get the specific workflow we want to test
    with open(_api_postprocessing_fixtures_path / "object_input_arguments.txt", "r") as f:
        raw_workflow = f.read()

    result = await component.process_raw_workflow(raw_workflow, None, config_map, "js", {}, [])

    assert result is not None
    assert isinstance(result.processed_workflow, PostGenApiWorkflow)

    assert isinstance(result.processed_workflow.input, dict)
    assert result.processed_workflow.input["type"] == "object"
    assert result.processed_workflow.input["properties"] == {}


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ["test_file", "activity_count", "expected_http_method"],
    [
        # HTTP VERBS for DAP should be wrapped in ${{...}}
        ("dap_http_request_wf.txt", 0, "${'GET'}"),
        # HTTP VERBS for RAW should be a constant string
        ("raw_http_request_wf.txt", 0, "GET"),
    ],
)
async def test_post_gen_sets_http_method_correctly(
    component: ApiWfPostGenerationProcessingService,
    config_map: dict[str, dict],
    model: BaseChatModel,
    test_file: str,
    activity_count: int,
    expected_http_method: str,
):
    """Test the API YAML parser with the given test file and assert that the parsing works."""
    # Get the test YAML content
    wf_path = _api_postprocessing_fixtures_path / test_file

    with open(wf_path, "r") as f:
        raw_workflow = f.read()

    result = await component.process_raw_workflow(raw_workflow, model, config_map, "js", {}, [], is_model_retry_permitted=False, can_augment_configmap=False)

    # Simple verification that parsing worked
    assert result is not None
    assert isinstance(result.processed_workflow, PostGenApiWorkflow)

    # check that the expected activities are present in the output workflow
    activity = result.processed_workflow.root.do[activity_count]
    if isinstance(activity, HttpRequest):
        assert activity.with_.method == expected_http_method
    elif isinstance(activity, ConnectorIntegrationWithMetadata):
        assert activity.with_.bodyParameters["method"] == expected_http_method
