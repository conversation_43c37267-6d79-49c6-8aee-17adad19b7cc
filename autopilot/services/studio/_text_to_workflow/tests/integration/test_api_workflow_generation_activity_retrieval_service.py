import pathlib

import pytest

from services.studio._text_to_workflow.api_workflow.services.api_wf_activity_retrieval_service import (
    APIActivityRetrievalService,
)
from services.studio._text_to_workflow.common.api_activity_retriever import (
    APIActivitiesRetriever,
)
from services.studio._text_to_workflow.common.api_workflow.schema import (
    ApiWorkflow,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils.json_utils import json_load_from_path
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import (
    ConnectionEmbeddingsRetriever,
)

_activity_retrieval_fixtures_path = conftest._fixtures_path / "api_activity_retrieval_fixtures"


@pytest.fixture
def embedding_model():
    """Fixture to provide an embedding model."""
    return ModelManager().get_embeddings_model("activities_embedding_model")


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "query, expected_activities, workflow_file",
    [
        (
            "Create a workflow to get time off requests from BambooHR for 'EmployeeId' between 'StartDate' and 'EndDate'",
            {"UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRGet_Time_off_Requests"},
            None,
        ),
        (
            "Get the cheapest flight from Bucharest to London and send it to a WhatsApp number via Twilio",
            {"UiPath.IntegrationService.Activities.Runtime.Activities.TwilioSend_WhatsApp_Message"},
            None,
        ),
        (
            "Instead of Netsuite, use Salesforce",
            {
                "UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceUpdate_Contact",
                "UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceSearch_using_SOQL",
            },
            _activity_retrieval_fixtures_path / "update_netsuite_contact.json",
        ),
        (
            "Create a workflow to make a dynamic http request to Salesforce",
            {"UiPath.IntegrationService.Activities.Runtime.Activities.Salesforce_HTTP_Request"},
            None,
        ),
        (
            "Create a workflow to get time off requests from BambooHR for 'EmployeeId' between 'StartDate' and 'EndDate'",
            {"UiPath.IntegrationService.Activities.Runtime.Activities.BambooHR_HTTP_Request"},
            None,
        ),
        (
            "Get Employee data with Workday Rest WQL, format it as HTML and send it by email via Office 365. Catch any errors and return an error message if any of the steps fail.",
            {
                "UiPath.IntegrationService.Activities.Runtime.Activities.Workday_RESTExecute_WQL_Query",
                "UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_Outlook_365Send_Email",
            },
            None,
        ),
        (
            "Create an API workflow to search in my Gmails and make a list of my subscription emails. For each email create a folder in drive",
            {
                "UiPath.IntegrationService.Activities.Runtime.Activities.Google_DriveCreate_Folder",
                "UiPath.IntegrationService.Activities.Runtime.Activities.GmailGet_Email_List",
            },
            None,
        ),
        (
            "Get all ServiceNow Business Units",
            {
                "UiPath.IntegrationService.Activities.Runtime.Activities.ServiceNowList_All_Business_Units",
            },
            None,
        ),
        # (
        #     # Curated ServiceNow Get Attachment Id morphs into Generic Salesforce Get Record (Attachment)
        #     "Instead of ServiceNow, get an attachment from Salesforce",
        #     {
        #         "UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceGet_Attachment",
        #     },
        #     _activity_retrieval_fixtures_path / "get_attachment_servicenow.json",
        # ),
    ],
)
async def test_get_relevant_activities(
    embedding_model,
    query: str,
    expected_activities: set[str],
    workflow_file: pathlib.Path | None,
    api_activities_retriever: APIActivitiesRetriever,
):
    """Test that generate_relevant_activities returns appropriate activities."""
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(api_activities_retriever, embedding_model)
    api_activities_retrieval_service = APIActivityRetrievalService(api_activities_retriever, connection_embeddings_retriever)

    # load the workflow if it is provided
    if workflow_file:
        raw_wf = json_load_from_path(workflow_file)
        workflow = ApiWorkflow.model_validate(raw_wf)
    else:
        workflow = None

    # Get relevant activities
    activities_retrieval_result = await api_activities_retrieval_service.generate_relevant_activities(
        query=query,
        workflow=workflow,
        connections=[],
        eval_mode=False,
    )

    assert len(activities_retrieval_result.retrieved_activities) > 0
    # we rarely should retrieve more than 30 activities - if needed a custom limit can be added for each test
    assert len(activities_retrieval_result.retrieved_activities) <= 30

    for expected in expected_activities:
        assert expected in activities_retrieval_result.retrieved_activities
