import copy
import json
from unittest import mock

import pytest

from services.studio._text_to_workflow.common import walkers
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils import errors, paths
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_endpoint
from services.studio._text_to_workflow.workflow_generation.config import constants
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    GenerateSequenceRequest,
    GenerateSequenceRequestPydantic,
)

REQUEST_TEMPLATE: GenerateSequenceRequest = {
    "userRequest": "",
    "connections": [],
    "targetFramework": "Portable",
    "objects": [],
    "workflowName": "test_workflow",
    "workflowDescription": "test_workflow_description",
    "projectDescription": "test_project_description",
    "isTestCase": False,
    "availableVariables": [],
    "availableWorkflows": [],
    "availableAdditionalTypeDefinitions": "",
    "currentWorkflow": "",
}


@pytest.mark.asyncio
async def test_send_email(send_email):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": send_email["description"],
            "currentWorkflow": yaml_dump(send_email["process"]),
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    for predicted_node in predicted_workflow["workflow"]:
        if predicted_node["activity"] != "UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections":
            assert "Assign" in predicted_node["activity"] or "LogMessage" in predicted_node["activity"]


@pytest.mark.asyncio
async def test_missing_workflow_request(missing_workflow):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": missing_workflow["description"],
            "currentWorkflow": yaml_dump(missing_workflow["process"]),
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    expected_acitivty = ["System.Activities.Statements.WriteLine", "UiPath.Core.Activities.LogMessage"]
    assert "errors" not in predicted_workflow, f"error found {predicted_workflow}"
    assert "workflow" in predicted_workflow, "no workflow"
    assert len(predicted_workflow["workflow"]) == 1, "empty workflow"
    assert predicted_workflow["workflow"][0]["activity"] in expected_acitivty, "wrong activity predicted"


@pytest.mark.asyncio
async def test_missing_current_activity_exception(missing_current_activity_workflow):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": missing_current_activity_workflow["description"],
            "currentWorkflow": yaml_dump(missing_current_activity_workflow["process"]),
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    with pytest.raises(errors.MissingCurrentActivityError):
        await workflow_generation_endpoint.generate_sequence(request_pydantic)


@pytest.mark.asyncio
async def test_invalid_workflow_yaml_exception():
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": "Log a Hello world message",
            "currentWorkflow": "[]",
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    with pytest.raises(errors.ExistingWorkflowEmptyError):
        await workflow_generation_endpoint.generate_sequence(request_pydantic)


@pytest.mark.asyncio
async def test_missing_user_prompt_throws_exception():
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": "",
            "currentWorkflow": "[]",
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    with pytest.raises(errors.UnprocessableEntityError):
        await workflow_generation_endpoint.generate_sequence(request_pydantic)


@pytest.mark.asyncio
async def test_invalid_user_prompt_throws_exception():
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": ".. !!",
            "currentWorkflow": "[]",
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    with pytest.raises(errors.UnprocessableEntityError):
        await workflow_generation_endpoint.generate_sequence(request_pydantic)


@pytest.mark.asyncio
async def test_use_excel_online(use_excel_online_workflow):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": use_excel_online_workflow["description"],
            "currentWorkflow": yaml_dump(use_excel_online_workflow["process"]),
            "targetFramework": use_excel_online_workflow["targetFramework"],
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    allowed_namespaces = tuple(constants.EXCLUSIVE_NAMESPACES["ExcelOnline"])
    assert all(
        act["activity"].startswith(allowed_namespaces) or act["activity"] == "UiPath.Core.Activities.LogMessage" for act in predicted_workflow["workflow"]
    ), f"Unexpected activities {', '.join([act['activity'] for act in predicted_workflow['workflow']])}"


@pytest.mark.asyncio
async def test_use_excel_desktop_legacy(use_excel_desktop_legacy_workflow):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": use_excel_desktop_legacy_workflow["description"],
            "currentWorkflow": yaml_dump(use_excel_desktop_legacy_workflow["process"]),
            "targetFramework": use_excel_desktop_legacy_workflow["targetFramework"],
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    allowed_namespaces = tuple(constants.EXCLUSIVE_NAMESPACES["ExcelDesktopLegacy"])
    assert all(act["activity"].startswith(allowed_namespaces) for act in predicted_workflow["workflow"]), (
        f"Unexpected activities {str.join(', ', [act['activity'] for act in predicted_workflow['workflow']])}"
    )


@pytest.mark.asyncio
async def test_use_excel_desktop_legacy_empty(use_excel_desktop_legacy_empty_workflow):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": use_excel_desktop_legacy_empty_workflow["description"],
            "currentWorkflow": yaml_dump(use_excel_desktop_legacy_empty_workflow["process"]),
            "targetFramework": use_excel_desktop_legacy_empty_workflow["targetFramework"],
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    allowed_namespaces = tuple(constants.EXCLUSIVE_NAMESPACES["ExcelDesktopLegacy"])
    assert all(act["activity"].startswith(allowed_namespaces) for act in predicted_workflow["workflow"]), (
        f"Unexpected activities {str.join(', ', [act['activity'] for act in predicted_workflow['workflow']])}"
    )


@pytest.mark.asyncio
async def test_use_excel_desktop_business(use_excel_desktop_business_workflow):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": use_excel_desktop_business_workflow["description"],
            "currentWorkflow": yaml_dump(use_excel_desktop_business_workflow["process"]),
            "targetFramework": use_excel_desktop_business_workflow["targetFramework"],
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    allowed_namespaces = tuple(constants.EXCLUSIVE_NAMESPACES["ExcelDesktopBusiness"])
    assert all(
        act["activity"].startswith(allowed_namespaces) or act["activity"].startswith("UiPath.Core.Activities.Assign") for act in predicted_workflow["workflow"]
    ), f"Unexpected activities {str.join(', ', [act['activity'] for act in predicted_workflow['workflow']])}"


@pytest.mark.asyncio
async def test_pruning_workflow(workflow_to_prune):
    # WARNING! This test will use a large number of tokens (around 16K)
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    workflow = workflow_to_prune["process"]["workflow"]
    before, current_activity, after = workflow
    left = []
    for i in range(100):
        act = copy.deepcopy(before)
        act["thought"] = f"Log Message before {i}"
        left.append(act)
    right = []
    for i in range(100):
        act = copy.deepcopy(after)
        act["thought"] = f"Log Message after {i}"
        right.append(act)
    workflow[:] = left + [current_activity] + right

    # prompt without any messages before and after current sequence has 8405 tokens at the moment
    # adding 20 before and 20 after messages, we have 10239 tokens
    # adding 50 before and 50 after messages, we have 12999 tokens
    # adding 100 before and 100 after messages, we have 18311 tokens, left with 16933 after the first pruning of the user message; it should prune from the right 64 activities in order to succeed
    request.update(
        {
            "userRequest": workflow_to_prune["description"],
            "currentWorkflow": yaml_dump(workflow_to_prune["process"]),
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    with mock.patch("services.studio._text_to_workflow.utils.inference.llm_gateway_model.LLMGatewayModel._ext_max_total_tokens", new=16384, create=True):
        predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    for predicted_node in predicted_workflow["workflow"]:
        if predicted_node["activity"] != "UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections":
            assert "Assign" in predicted_node["activity"] or "LogMessage" in predicted_node["activity"], f"Unexpected activity {predicted_node['activity']}"


@pytest.mark.asyncio
async def test_switch_case(switch_sequence):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    request.update(
        {
            "userRequest": switch_sequence["description"],
            "currentWorkflow": yaml_dump(switch_sequence["process"]),
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    predicted_worklow_yaml = (await workflow_generation_endpoint.generate_sequence(request_pydantic))["result"]
    predicted_workflow = yaml_load(predicted_worklow_yaml)
    for predicted_node in predicted_workflow["workflow"]:
        if predicted_node["activity"] != "UiPath.MicrosoftOffice365.Activities.Mail.SendMailConnections":
            assert "Assign" in predicted_node["activity"] or "LogMessage" in predicted_node["activity"]


@pytest.mark.asyncio
async def test_long_uia_testcase(long_uia_sequence):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    objects = []
    with open(paths.get_dataset_object_repository_cache_path()) as json_file:
        objects = json.load(json_file)["objects"]

    request.update(
        {
            "userRequest": long_uia_sequence["userRequest"],
            "currentWorkflow": "",
            "objects": objects,
            "isTestCase": True,
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    sequence_result = await workflow_generation_endpoint.generate_sequence(request_pydantic)
    assert sequence_result["prompt_class"] == "valid"
    assert len(sequence_result["retrieved_activities"]) < 25, "Too many activities retrieved"

    predicted_workflow = Workflow(long_uia_sequence["userRequest"], "", yaml_load(sequence_result["result"]))
    collector = walkers.ActivityTypeCollector().walk(predicted_workflow)

    # TODO: Gemini build the activity tree well, including Get Text and its prompt,
    # but the UIA task on gpt4o sometimes forgets to add the proper action for Get Text in <10% of cases.
    # Review once UIA uses Gemini and re-add UiPath.UIAutomationNext.Activities.NGetText.
    for activity_type_name in long_uia_sequence["expectedActivities"]:
        assert activity_type_name in collector.typed_activity_instances, (
            f"Activities of type {activity_type_name} not found in the predicted workflow. Available types: {str.join(', ', collector.typed_activity_instances)}. Workflow: {predicted_workflow.lmyaml()}"
        )


@pytest.mark.asyncio
async def test_excel_uia_testcase(excel_uia_sequence):
    request: GenerateSequenceRequest = copy.deepcopy(REQUEST_TEMPLATE)
    objects = []
    with open(paths.get_dataset_object_repository_cache_path()) as json_file:
        objects = json.load(json_file)["objects"]

    request.update(
        {
            "userRequest": excel_uia_sequence["userRequest"],
            "currentWorkflow": "",
            "objects": objects,
            "isTestCase": True,
            "targetFramework": "Windows",
        }
    )
    request_pydantic = GenerateSequenceRequestPydantic.model_validate(request)

    sequence_result = await workflow_generation_endpoint.generate_sequence(request_pydantic)
    assert sequence_result["prompt_class"] == "valid"
    assert len(sequence_result["retrieved_activities"]) < 25, "Too many activities retrieved"

    predicted_workflow = Workflow(excel_uia_sequence["userRequest"], "", yaml_load(sequence_result["result"]))
    collector = walkers.ActivityTypeCollector().walk(predicted_workflow)

    # TODO: Gemini build the activity tree well, including Get Text and its prompt,
    # but the UIA task on gpt4o sometimes forgets to add the proper action for Get Text in <10% of cases.
    # Review once UIA uses Gemini and re-add UiPath.UIAutomationNext.Activities.NGetText.
    for activity_type_name in excel_uia_sequence["expectedActivities"]:
        assert activity_type_name in collector.typed_activity_instances, (
            f"Activities of type {activity_type_name} not found in the predicted workflow. Available types: {str.join(', ', collector.typed_activity_instances)}. Workflow: {predicted_workflow.lmyaml()}"
        )
