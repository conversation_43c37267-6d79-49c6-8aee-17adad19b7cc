from unittest.mock import Mock

import pytest

from services.studio._text_to_workflow.api_workflow.summarization.workflow_generation_summarizer import WorkflowGenerationSummarizer
from services.studio._text_to_workflow.api_workflow.summarization.workflow_generation_summary_schema import WorkflowGenerationSummary
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import (
    ConnectorIntegrationMetadata,
    ConnectorIntegrationWithMetadata,
    ConnectorRequestParameters,
    PostGenApiWorkflow,
    PostGenSequence,
)
from services.studio._text_to_workflow.common.api_workflow.schema import HTTPCallParameters, HttpRequest


@pytest.fixture
def mock_activities_retriever():
    """Create a mock APIActivitiesRetriever for testing."""
    retriever = Mock(spec=APIActivitiesRetriever)
    return retriever


@pytest.fixture
def summarizer(mock_activities_retriever):
    """Create a WorkflowGenerationSummarizer instance for testing."""
    return WorkflowGenerationSummarizer(mock_activities_retriever)


@pytest.fixture
def base_workflow():
    """Create a base workflow structure for testing."""
    return PostGenApiWorkflow(input=None, root=PostGenSequence(thought="Root sequence", id="root", activity="Sequence", do=[]))


class TestWorkflowGenerationSummarizer:
    """Test suite for WorkflowGenerationSummarizer class."""

    @pytest.mark.parametrize(
        "input_config,expected_input_names",
        [
            # Test case: no input
            (None, []),
            # Test case: empty input properties
            ({"properties": {}}, []),
            # Test case: with input properties
            ({"properties": {"user_id": {"type": "string"}, "email": {"type": "string"}, "count": {"type": "integer"}}}, ["user_id", "email", "count"]),
        ],
    )
    def test_summarize_postgen_workflow_input_handling(
        self,
        summarizer: WorkflowGenerationSummarizer,
        base_workflow: PostGenApiWorkflow,
        input_config: dict,
        expected_input_names: list[str],
    ):
        """Test summarizing workflows with different input configurations."""
        base_workflow.input = input_config

        result = summarizer.summarize_postgen_workflow(base_workflow)

        assert isinstance(result, WorkflowGenerationSummary)
        assert set(result.input_names) == set(expected_input_names)

    @pytest.mark.parametrize(
        "body_params,mock_return,expected_contains,expected_count",
        [
            # Test case: connector HTTP request with URL
            (
                {"method": '${"POST"}', "url": '${"https://api.example.com/users"}'},
                {"category": "TestApp"},
                "Make HTTP request: Using pre-authenticated HTTP call TestApp_HTTP_Request with POST https://api.example.com/users (application: TestApp)",
                1,
            ),
            # Test case: connector HTTP request with no URL
            ({"method": '${"GET"}', "url": ""}, {"category": "TestApp"}, "and the URL is not set", 1),
            # Test case: connector HTTP request with empty URL
            ({"method": '${"GET"}', "url": '${""}'}, {"category": "TestApp"}, "and the URL is not set", 1),
        ],
    )
    def test_summarize_postgen_workflow_connector_http_requests(
        self,
        summarizer: WorkflowGenerationSummarizer,
        base_workflow: PostGenApiWorkflow,
        mock_activities_retriever: Mock,
        body_params: dict,
        mock_return: dict,
        expected_contains: str,
        expected_count: int,
    ):
        """Test summarizing workflows with connector HTTP request activities."""
        mock_activities_retriever.get_by_class_name.return_value = mock_return

        connector_activity = ConnectorIntegrationWithMetadata(
            thought="Make HTTP request",
            id="http_request_1",
            activity="TestApp_HTTP_Request",
            with_=ConnectorRequestParameters(connectionId="conn_123", pathParameters={}, queryParameters={}, bodyParameters=body_params),  # pyright:ignore
            metadata=ConnectorIntegrationMetadata(),
        )

        base_workflow.root.do = [connector_activity]

        result = summarizer.summarize_postgen_workflow(base_workflow)

        assert result.input_names == []
        assert len(result.api_calls) == expected_count
        if expected_count > 0:
            assert expected_contains in result.api_calls[0]
        mock_activities_retriever.get_by_class_name.assert_called_once_with("TestApp_HTTP_Request", "activity")

    @pytest.mark.parametrize(
        "activity_config,mock_return,expected_contains,expected_count",
        [
            # Test case: curated connector
            (
                {
                    "activity_name": "SlackSend_Message",
                    "thought": "Send Slack message",
                },
                {"category": "SlackApp"},
                "Send Slack message: Using curated connector SlackSend_Message (application: SlackApp)",
                1,
            ),
            # Test case: activity not found
            (
                {
                    "activity_name": "UnknownConnector",
                    "thought": "Unknown activity",
                },
                None,
                "",
                0,
            ),
        ],
    )
    def test_summarize_postgen_workflow_connector_activities(
        self,
        summarizer: WorkflowGenerationSummarizer,
        base_workflow: PostGenApiWorkflow,
        mock_activities_retriever: Mock,
        activity_config: dict,
        mock_return: dict,
        expected_contains: str,
        expected_count: int,
    ):
        """Test summarizing workflows with different connector activity types."""
        mock_activities_retriever.get_by_class_name.return_value = mock_return

        connector_activity = ConnectorIntegrationWithMetadata(
            thought=activity_config["thought"],
            id="activity_1",
            activity=activity_config["activity_name"],
            with_=ConnectorRequestParameters(pathParameters={}, queryParameters={}, bodyParameters={}),  # pyright:ignore
            metadata=ConnectorIntegrationMetadata(),
        )

        base_workflow.root.do = [connector_activity]

        result = summarizer.summarize_postgen_workflow(base_workflow)

        assert result.input_names == []
        assert len(result.api_calls) == expected_count
        if expected_count > 0:
            assert expected_contains in result.api_calls[0]

    @pytest.mark.parametrize(
        "http_config,expected_contains,expected_count",
        [
            # Test case: HTTP request with endpoint
            (
                {"method": "PUT", "endpoint": '${"https://jsonplaceholder.typicode.com/posts/1"}', "thought": "Direct HTTP call"},
                "Direct HTTP call: Using HTTP call with PUT https://jsonplaceholder.typicode.com/posts/1",
                1,
            ),
            # Test case: HTTP request with no endpoint
            ({"method": "GET", "endpoint": "", "thought": "HTTP call without endpoint"}, "and the URL is not set", 1),
        ],
    )
    def test_summarize_postgen_workflow_http_requests(
        self,
        summarizer: WorkflowGenerationSummarizer,
        base_workflow: PostGenApiWorkflow,
        http_config: dict,
        expected_contains: str,
        expected_count: int,
    ):
        """Test summarizing workflows with HttpRequest activities."""
        http_activity = HttpRequest(
            thought=http_config["thought"],
            id="http_call_1",
            activity="HttpRequest",
            with_=HTTPCallParameters(method=http_config["method"], endpoint=http_config["endpoint"]),  # pyright:ignore
        )

        base_workflow.root.do = [http_activity]

        result = summarizer.summarize_postgen_workflow(base_workflow)

        assert result.input_names == []
        assert len(result.api_calls) == expected_count
        if expected_count > 0:
            assert expected_contains in result.api_calls[0]

    def test_summarize_postgen_workflow_mixed_activities(
        self,
        summarizer: WorkflowGenerationSummarizer,
        base_workflow: PostGenApiWorkflow,
        mock_activities_retriever: Mock,
    ):
        """Test summarizing a workflow with mixed activity types."""
        base_workflow.input = {"properties": {"input1": {"type": "string"}}}

        # Setup mock
        mock_activities_retriever.get_by_class_name.side_effect = lambda activity_name, _: (
            {"category": "TestApp"} if activity_name == "TestApp_HTTP_Request" else {"category": "SlackApp"} if activity_name == "SlackSend_Message" else None
        )

        # Create mixed activities
        http_connector = ConnectorIntegrationWithMetadata(
            thought="HTTP connector",
            id="testapp_1",
            activity="TestApp_HTTP_Request",
            with_=ConnectorRequestParameters(  # pyright:ignore
                connectionId="conn_1", pathParameters={}, queryParameters={}, bodyParameters={"method": '${"POST"}', "url": '${"https://api.test.com"}'}
            ),  # pyright:ignore
            metadata=ConnectorIntegrationMetadata(),
        )

        curated_connector = ConnectorIntegrationWithMetadata(
            thought="Slack connector",
            id="slack_1",
            activity="SlackSend_Message",
            with_=ConnectorRequestParameters(connectionId="slack_conn", pathParameters={}, queryParameters={}, bodyParameters={"channel": "general"}),  # pyright:ignore
            metadata=ConnectorIntegrationMetadata(),
        )

        direct_http = HttpRequest(
            thought="Direct HTTP",
            id="http_2",
            activity="HttpRequest",
            with_=HTTPCallParameters(method="GET", endpoint='${"https://direct.api.com"}'),  # pyright:ignore
        )

        base_workflow.root.do = [http_connector, curated_connector, direct_http]

        result = summarizer.summarize_postgen_workflow(base_workflow)

        assert result.input_names == ["input1"]
        assert len(result.api_calls) == 3
        assert any("HTTP connector: Using pre-authenticated HTTP call" in call for call in result.api_calls)
        assert any("Slack connector: Using curated connector" in call for call in result.api_calls)
        assert any("Direct HTTP: Using HTTP call with GET" in call for call in result.api_calls)
