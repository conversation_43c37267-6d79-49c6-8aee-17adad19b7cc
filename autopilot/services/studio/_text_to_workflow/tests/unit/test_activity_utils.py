import pytest

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.utils.dynamic_activity_utils import is_generic_dynamic_activity


@pytest.mark.asyncio
@pytest.mark.parametrize(
    ["activity_name", "is_generic"],
    [
        ("UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceGet_Lead", False),
        ("UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_Outlook_365Send_Email", False),
        ("UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceSearch_Records", True),
        ("UiPath.IntegrationService.Activities.Runtime.Activities.WorkableInsert_Record", True),
        ("UiPath.IntegrationService.Activities.Runtime.Activities.GitHubReplace_Record", True),
        ("UiPath.IntegrationService.Activities.Runtime.Activities.Oracle_NetSuite_HTTP_Request", False),
    ],
)
async def test_is_generic_dynamic_activity(api_activities_retriever: APIActivitiesRetriever, activity_name: str, is_generic: bool):
    # Get some real activity definitions
    activity = api_activities_retriever.get_by_class_name(activity_name, "activity")

    # Test a generic dynamic activity
    if is_generic:
        assert activity is None
        return
    assert activity is not None
    assert is_generic_dynamic_activity(activity["fullClassName"], activity["activityConfiguration"]) == is_generic
