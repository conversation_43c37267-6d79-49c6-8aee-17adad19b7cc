import pytest

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.api_activity_retriever import (
    APIActivitiesRetriever,
)
from services.studio._text_to_workflow.common.schema import (
    ActivityDefinition,
    ActivitySearchOptions,
    PlanStep,
)
from services.studio._text_to_workflow.models.model_manager import ModelManager


@pytest.fixture
def retriever():
    """Fixture to provide an instance of APIActivitiesRetriever."""
    api_retriever = APIActivitiesRetriever()
    api_retriever.build()
    return api_retriever


@pytest.fixture
def embedding_model():
    """Fixture to provide an embedding model."""
    return ModelManager().get_embeddings_model("activities_embedding_model")


@pytest.mark.parametrize(
    "description,generic_activities",
    [
        ("Search on youtube for the latest videos about AI", False),
        ("Search on youtube for the latest videos about AI", True),
    ],
)
def test_api_activity_retriever_returns_allowed_activities(retriever, embedding_model, description, generic_activities):
    """
    Test that the API activity retriever returns only allowed activities.
    If generic_activities is True, we expect to find generic activities only.
    If generic_activities is False, we expect to find curated activities only.
    """
    # Create a plan step with the given description
    embeddings = embedding_model.encode([description])
    step = PlanStep(text=description, embedding=embeddings, triggers=[], activities=[])

    # Search for activities
    activities = retriever.search(
        step=step,
        connections=[],
        activity_search_options=ActivitySearchOptions(
            mode=None,
            target_framework="Portable",
            ignored_namespaces=set(),
            ignored_activities=set(),
            generic_activities=generic_activities,
        ),
        activity_type="activity",
        k=5,
    )

    # If we expect to find something, check we got at least one result
    assert len(activities) >= 1

    # Check the activity has a name
    for activity in activities:
        activity_def: ActivityDefinition = activity
        assert activity_def["fullActivityName"] in constants.API_ACTIVITY_NAMES
        # Check that activities have the expected generic flag
        assert activity_def["genericActivity"] == generic_activities


def test_api_activity_retriever_does_not_return_excluded_activities(retriever: APIActivitiesRetriever):
    """Test that the API activity retriever doesn't return excluded activities"""
    # Create a plan step with the given description

    for activity_name in constants.ACTIVITIES_EXCLUDED:
        activity = retriever.get_by_class_name(constants.DAP_NAMESPACE + "." + activity_name, "activity")
        assert activity is None

    for activity_name in ["Microsoft_Azure_OpenAIGenerate_Chat_Completion", "OpenAIGenerate_Chat_Completion"]:
        activity = retriever.get_by_class_name(constants.DAP_NAMESPACE + "." + activity_name, "activity")
        assert activity is not None
