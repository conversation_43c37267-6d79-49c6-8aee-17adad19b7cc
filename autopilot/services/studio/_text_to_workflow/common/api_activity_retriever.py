import copy

import tqdm

from services.studio._text_to_workflow.common import build_embeddings, constants
from services.studio._text_to_workflow.common.activity_retriever import (
    ActivitiesIndexBase,
    ActivitiesRetrieverBase,
)
from services.studio._text_to_workflow.common.schema import (
    ActivityDefinition,
    ActivityRetrieverState,
    ActivitySearchOptions,
    ActivityType,
    Connection,
    EmbeddingsActivityFilterOptions,
    PlanStep,
    TargetFramework,
)
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import errors, paths, telemetry_utils
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel

LOGGER = telemetry_utils.AppInsightsLogger()


class APIActivitiesRetriever(ActivitiesRetrieverBase):
    _singleton_instance: "APIActivitiesRetriever | None" = None

    def __new__(cls) -> "APIActivitiesRetriever":
        if cls._singleton_instance is None:
            cls._singleton_instance = super(APIActivitiesRetriever, cls).__new__(cls)
            cls._singleton_instance._initialize()
        return cls._singleton_instance

    def _initialize(self) -> None:
        super()._initialize()
        embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")

        if not hasattr(self, "index"):
            self.index = {
                "activity": CuratedApiActivitiesIndex(embedding_model, "activity"),
                "generic-activity": GenericApiActivitiesIndex(embedding_model, "generic-activity"),
            }
        self.db_hash = None

    def get_activity_info(self, activity_id: str, activity_type: ActivityType) -> ActivityDefinition | None:
        return self.get_by_activity_id(activity_id, activity_type)

    def activity_exists(self, activity_name: str, activity_type: ActivityType) -> tuple[bool, bool]:
        """Base method to check if an activity exists in a given index

        Args:
            activity_name: The name of the activity to check
            index_key: The key to look up the index. Could be a tuple (target_framework, activity_type)
            or just activity_type depending on the subclass implementation

        Returns:
            A tuple of (exists_by_activity_id, exists_by_class_name)
        """
        if activity_type not in self.index:
            return (False, False)

        index = self.get_index(activity_type)
        return self.activity_exists_in_index(activity_name, index)

    def get_by_activity_id(
        self,
        item_full_activity_id: str,
        activity_type: ActivityType | None = None,
    ) -> ActivityDefinition | None:
        if activity_type is not None:
            index = self.index.get(activity_type)
            if index is not None:
                return index.get_by_full_activity_id(item_full_activity_id)

            return None

        for activity_type in ("generic-activity", "activity"):
            if index := self.index.get(activity_type):
                if item := index.get_by_full_activity_id(item_full_activity_id):
                    return item

        return None

    def get_by_class_name(
        self,
        activity_class_name: str,
        activity_type: ActivityType | None = None,
    ) -> ActivityDefinition | None:
        # If specific activity type provided, only search in that index
        if activity_type is not None:
            if index := self.index.get(activity_type):
                return index.get_by_full_class_name(activity_class_name)
            return None

        # Search across all activity types if no specific type provided
        activity_types = ("activity", "generic-activity")
        for activity_type in activity_types:
            if index := self.index.get(activity_type):
                if item := index.get_by_full_class_name(activity_class_name):
                    return item
        return None

    def search(
        self,
        step: PlanStep,
        connections: list[Connection],
        activity_search_options: ActivitySearchOptions,
        activity_type: ActivityType,
        k: int,
    ) -> list[ActivityDefinition]:
        index = self.index.get("generic-activity" if activity_search_options.get("generic_activities", False) else "activity")

        if index is None:
            return []
        return self._search_in_index(
            index,
            step,
            connections,
            activity_search_options["ignored_namespaces"],
            activity_search_options["ignored_activities"],
            k,
        )

    def get_relevant(
        self,
        steps: list[PlanStep],
        connections: list[Connection],
        activity_search_options: ActivitySearchOptions,
        k: int = 10,
    ) -> tuple[list[PlanStep], list[ActivityDefinition], list[ActivityDefinition], list[str]]:
        steps = copy.deepcopy(steps)
        packages = set()
        basic_activities = self.index["activity"].get_basic_activities()

        # For API activities, we only search for activities (no triggers)

        for i, step in enumerate(steps):
            relevant_activities = self.search(step, connections, activity_search_options, "activity", k)
            step["activities"] = relevant_activities
            for activity in relevant_activities:
                activity["step"] = i + 1
                packages.add(activity["packageName"])

        return steps, [], basic_activities, list(packages)

    def get_index(self, activity_type: ActivityType) -> "ActivitiesIndexBase":
        return self.index[activity_type]

    def get_all_connectors(self) -> set[str]:
        connectors = set()
        for activity in self.index["activity"].store.state["activities"]:
            if activity["connectorKey"] is not None:
                connectors.add(activity["connectorKey"])

        return connectors

    def get_basic_http_activity_by_connectors_key(self, connectors_key: set[str]) -> list[ActivityDefinition]:
        index = self.index["activity"]
        activities: list[ActivityDefinition] = []
        for activity in index.store.state["activities"]:
            if activity["connectorKey"] in connectors_key and activity["fullActivityName"] == constants.DAP_HTTP_ACTIVITY_NAME:
                activities.append(activity)
        return activities


class APIActivitiesIndexBase(ActivitiesIndexBase):
    target_framework: TargetFramework = "Api"

    def __init__(self, embedding_model: EmbeddingModel, activity_type: ActivityType) -> None:
        super().__init__(
            embedding_model,
            self.target_framework,
            activity_type,
            paths.get_api_activity_retriever_path(),
        )

    def build(self) -> tuple[ActivityRetrieverState, dict]:
        return self._build_with_options(False, False)

    def _build_with_options(self, generic_activities_only: bool, curated_activities_only: bool) -> tuple[ActivityRetrieverState, dict]:
        tqdm.tqdm.write(f"Started building {self.target_framework}.{self.activity_type} activities.")
        state = build_embeddings.build(
            self.activity_type,
            self.embedding_model,
            self.target_framework,
            show_progress_bar=settings.DEBUG_MODE,
            activity_filter_options=EmbeddingsActivityFilterOptions(
                allowed_activity_names=constants.API_ACTIVITY_NAMES,
                include_dynamic_http_activities=True,
                activities_to_exclude=constants.ACTIVITIES_EXCLUDED,
                generic_activities_only=generic_activities_only,
                curated_activities_only=curated_activities_only,
            ),
        )
        if not state["activities"]:
            raise errors.EmptyActivitiesIndexError(self.target_framework, self.activity_type)
        tqdm.tqdm.write(f"Built {self.target_framework}.{self.activity_type} index with size {len(state['activities'])}.")
        state_info = {
            k: state[k]
            for k in (
                "fullactivityid2index",
                "fullclassname2index",
                "classname2fullclassnames",
                "activities",
            )
        }
        return state, state_info

    def get_basic_activities(self) -> list[ActivityDefinition]:
        return self._basic_activities

    def search(
        self,
        step: PlanStep,
        connections: list[Connection],
        ignored_namespaces: set[str],
        ignored_activities: set[str],
        k: int = 5,
        accepted_dap_activities_names: set[str] = constants.API_ACTIVITY_NAMES,
        dinamyc_activities_procent_scores: dict[str, float] = {"description": 0.6, "category": 0.1, "display_name": 0.3},
    ) -> tuple[list[ActivityDefinition], list[float]]:
        return super().search(
            step,
            connections,
            ignored_namespaces,
            ignored_activities,
            k,
            accepted_dap_activities_names=accepted_dap_activities_names,
            dinamyc_activities_procent_scores=dinamyc_activities_procent_scores,
        )


class CuratedApiActivitiesIndex(APIActivitiesIndexBase):
    def build(self) -> tuple[ActivityRetrieverState, dict]:
        return self._build_with_options(False, True)


class GenericApiActivitiesIndex(APIActivitiesIndexBase):
    def build(self) -> tuple[ActivityRetrieverState, dict]:
        return self._build_with_options(True, False)
