import abc

import typing_extensions as t
from overrides import override

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.constants import ANY_OUTPUT_PATH_REGEX, STATIC_PROPERTIES
from services.studio._text_to_workflow.common.api_workflow.schema import (
    BASE_API_ACTIVITIES,
    ApiWorkflow,
    BaseModel,
    BaseWorkflowActivity,
    ForEach,
    If,
    Sequence,
    TryCatch,
)
from services.studio._text_to_workflow.utils.activity_utils import remove_dynamic_activity_prefix_from_name


class ApiWorkflowWalker(abc.ABC):  # noqa: B024
    """
    Simplified walker interface for traversing API workflows.
    Override the processing methods to implement the desired behavior.
    """

    def __init__(self):
        self.reset()

    def _process_activity(self, activity: BaseWorkflowActivity) -> None:
        """Process the current activity."""
        pass

    def reset(self):
        pass

    def _get_child_sequences(self, activity: BaseWorkflowActivity) -> list[list[BASE_API_ACTIVITIES]]:
        """Get all child sequences from the current activity."""
        children: list[list[BASE_API_ACTIVITIES]] = []

        if isinstance(activity, Sequence):
            children.append(activity.do)
        elif isinstance(activity, ForEach):
            children.append(activity.do)
        elif isinstance(activity, If):
            children.append(activity.then)
            if activity.else_:
                children.append(activity.else_)
        elif isinstance(activity, TryCatch):
            children.append(activity.try_)
            children.append(activity.catch.do)

        return children

    def _walk_activity(self, activity: BaseWorkflowActivity) -> None:
        """Walk the activity tree."""
        self._process_activity(activity)

        children = self._get_child_sequences(activity)
        for child_sequence in children:
            self._walk_activity_sequence(child_sequence)

    def _walk_activity_sequence(self, child_sequence: list[BASE_API_ACTIVITIES]) -> None:
        for child_activity in child_sequence:
            self._walk_activity(child_activity)

    def walk(self, workflow: ApiWorkflow) -> t.Self:
        """Walk the workflow and return self for chaining."""
        self._walk_activity(workflow.root)
        return self


class ApiActivityIdCollector(ApiWorkflowWalker):
    """Collects all activity IDs from an API workflow."""

    @override
    def reset(self):
        self.ids = set()

    def _process_activity(self, activity: BaseWorkflowActivity) -> None:
        """Add the current activity's ID to the list."""
        self.ids.add(activity.id)

    def collect(self, workflow: ApiWorkflow) -> set[str]:
        """Walk the workflow and return all activity IDs."""
        self.reset()
        self.walk(workflow)
        return self.ids

    def collect_from_activity_list(self, activity_list: list[BASE_API_ACTIVITIES]) -> set[str]:
        """Walk the activity list and return all activity IDs."""
        self.reset()
        self._walk_activity_sequence(activity_list)
        return self.ids


class ApiWorkflowExpressionRemover(ApiWorkflowWalker):
    """Removes all expressions from an API workflow that reference certain activity IDs."""

    def __init__(self, activity_ids_to_remove: set[str]):
        super().__init__()
        self.activity_ids_to_remove = activity_ids_to_remove

    def _should_remove_expression(self, expression: str) -> bool:
        """Check if an expression references any activity ID that needs to be removed."""
        # Find all context output path references in the expression
        path_matches = ANY_OUTPUT_PATH_REGEX.findall(expression)
        for segment in path_matches:
            if segment in self.activity_ids_to_remove:
                return True
        return False

    def _process_activity(self, activity: BaseWorkflowActivity) -> None:
        """Remove expressions that reference activity IDs in self.activity_ids_to_remove"""

        for field_name in activity.model_fields:
            # Skip static properties
            if field_name in STATIC_PROPERTIES:
                continue

            # Get the field value using getattr
            field_value = getattr(activity, field_name)

            # Skip if the field is an activity or a list of activities
            if isinstance(field_value, BaseWorkflowActivity) or (
                isinstance(field_value, list) and any(isinstance(x, BaseWorkflowActivity) for x in field_value)
            ):
                continue

            # Handle dictionaries (e.g. 'with' field)
            if isinstance(field_value, dict):
                for key, value in field_value.items():
                    if isinstance(value, str) and self._should_remove_expression(value):
                        field_value[key] = ""

            # Handle BaseModel fields (e.g. 'for' properties)
            if isinstance(field_value, BaseModel):
                for nested_field_name in field_value.model_fields:
                    nested_field_value = getattr(field_value, nested_field_name)
                    if isinstance(nested_field_value, str) and self._should_remove_expression(nested_field_value):
                        setattr(field_value, nested_field_name, "")

            # Handle simple string expressions
            if isinstance(field_value, str) and self._should_remove_expression(field_value):
                setattr(activity, field_name, "")

    def process_workflow(self, workflow: ApiWorkflow):
        """Walk the workflow and remove all expressions that reference the given activity IDs."""
        self.reset()
        self.walk(workflow)


class ApiWorkflowActivityIdReplace(ApiWorkflowWalker):
    def __init__(self):
        self.activities_retriever = APIActivitiesRetriever()

    def _process_activity(self, activity: BaseWorkflowActivity) -> None:
        if activity.get_activity_name() in ["Http", "Sequence", "ForEach", "JsInvoke", "If", "Connector", "Response", "TryCatch"]:
            activity.thought = activity.get_activity_name()
        else:
            activity_def = self.activities_retriever.get_by_class_name(activity.get_activity_name(), "activity")
            if activity_def is not None:
                activity.thought = remove_dynamic_activity_prefix_from_name(activity_def["fullClassName"])
