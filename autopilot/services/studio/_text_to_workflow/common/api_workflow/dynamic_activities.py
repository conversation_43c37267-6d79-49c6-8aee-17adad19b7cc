import json

import services.studio._text_to_workflow.common.api_activity_retriever as ar
import services.studio._text_to_workflow.common.api_workflow.schema as api_wf_schema
import services.studio._text_to_workflow.common.constants as ct
import services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component as dac


async def get_dynamic_activity_schemas(
    activities: list[api_wf_schema.ConnectorIntegration],
    metadata: dict,
    activities_retriever: ar.APIActivitiesRetriever,
) -> dict:
    """Get the connector activities schemas."""
    activity_definitions_by_fqn, connections, id_fqn_pairs = {}, {}, []
    for activity in activities:
        activity_fqn = ct.DAP_NAMESPACE + "." + activity.activity

        activity_def = activities_retriever.get_by_class_name(activity_fqn)
        if activity_fqn not in activity_definitions_by_fqn:
            activity_definitions_by_fqn[activity_fqn] = activity_def
            id_fqn_pairs.append((activity.id, activity_fqn))
        activity_metadata = metadata[activity.id]
        activity_connection = {"connectionId": activity_metadata["connectionId"], "connector": activity_metadata["connector"]}
        connections[activity_metadata["connector"]] = activity_connection

    _, activity_definitions_by_fqn = await dac.WorkflowGenerationDynamicActivitiesComponent.augment_type_definitions_for_dynamic_activities(
        activity_definitions_by_fqn,
        {},
        connections,
        force_is_package_name=ct.FORCED_IS_PACKAGE_NAME,
        force_is_package_version=ct.FORCED_IS_PACKAGE_VERSION,
    )

    activity_json_schemas = {}
    for activity_id, activity_fqn in id_fqn_pairs:
        activity_definition = activity_definitions_by_fqn[activity_fqn]
        if json_schema := activity_definition.get("jsonSchema"):
            activity_json_schemas[activity_id] = json.loads(json_schema)

    return activity_json_schemas
