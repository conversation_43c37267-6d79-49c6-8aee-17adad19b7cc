import copy
import json
import re
from collections import defaultdict
from typing import Any

from typing_extensions import Literal, get_args

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import (
    POST_GENERATION_ACTIVITIES,
    PostGenApiWorkflow,
    PostGenForEach,
    PostGenIf,
    PostGenSequence,
    PostGenTryCatch,
)
from services.studio._text_to_workflow.common.api_workflow.schema import (
    BASE_API_ACTIVITIES,
    BREAK_ACTIVITY_TYPE,
    DO_WHILE_ACTIVITY_TYPE,
    FOR_EACH_ACTIVITY_TYPE,
    HTTP_ACTIVITY_TYPE,
    IF_ACTIVITY_TYPE,
    JS_INVOKE_ACTIVITY_TYPE,
    RESPONSE_ACTIVITY_TYPE,
    SEQUENCE_ACTIVITY_TYPE,
    TRY_CATCH_ACTIVITY_TYPE,
    ApiWorkflow,
    ApiWorkflowForActivityEdit,
    BaseWorkflowActivity,
    Break,
    ConnectorIntegration,
    Do<PERSON><PERSON>e,
    ForEach,
    HttpRequest,
    If,
    JsInvoke,
    Response,
    Sequence,
    TryCatch,
)
from services.studio._text_to_workflow.common.embeddingsdb import create_dynamic_activity_full_id
from services.studio._text_to_workflow.common.schema import (
    ActivityType,
)
from services.studio._text_to_workflow.utils.activity_utils import add_dynamic_activity_prefix_to_name, remove_dynamic_activity_prefix_from_name
from services.studio._text_to_workflow.utils.dict_utils import get_sole_item_from_dict

# Constants
EXPORT_PROPERTY_PATTERN = re.compile(r'outputs.*?"?([a-zA-Z0-9_-]+)"?\s*[:=]')
RAW_API_ACTIVITY_TYPES = Literal["Http", "Sequence", "ForEach", "JsInvoke", "If", "Connector", "Response", "TryCatch", "DoWhile", "Break"]
DAP_HTTP_OBJECT_NAME = "httpRequest"


def get_do_block(activity: BaseWorkflowActivity | None) -> list[BASE_API_ACTIVITIES]:
    if not isinstance(activity, (Sequence, ForEach, DoWhile)):
        return []

    return activity.do or []


def get_activity_types_from_do_block(do_block: list[BASE_API_ACTIVITIES]) -> set[str]:
    """Recursively extract all activity types from a do block."""
    result = set()
    for activity in do_block:
        result |= get_workflow_activity_types(activity)
    return result


def get_workflow_activity_types(root_activity: BASE_API_ACTIVITIES) -> set[str]:
    """Extract all activity types from an API workflow activity dictionary."""
    result = set()

    # Get the activity type - use getattr to safely check for activityType
    if isinstance(root_activity, ConnectorIntegration):
        # the connector activity type should have the full activity type name
        activity_type = add_dynamic_activity_prefix_to_name(root_activity.activity)
    else:
        assert isinstance(root_activity, BaseWorkflowActivity)
        activity_type = root_activity.activity

    if activity_type and activity_type != SEQUENCE_ACTIVITY_TYPE:
        result.add(activity_type)

    if isinstance(root_activity, If):
        for activity in root_activity.then:
            result.update(get_workflow_activity_types(activity))
        for activity in root_activity.else_:
            result.update(get_workflow_activity_types(activity))
        return result

    if isinstance(root_activity, TryCatch):
        for activity in root_activity.try_:
            result.update(get_workflow_activity_types(activity))
        for activity in root_activity.catch.do:
            result.update(get_workflow_activity_types(activity))
        return result

    if isinstance(root_activity, Break):
        return result

    # Handle nested activities in the 'do' block
    do_block = get_do_block(root_activity)
    if do_block is None:
        return result

    result.update(get_activity_types_from_do_block(do_block))
    return result


def _extract_activity_type(activity_data: dict[str, Any]) -> str | None:
    """Extract the activity type from the metadata."""
    metadata: dict = activity_data.get("metadata", {})
    activity_type = metadata.get("activityType", "")
    if activity_type in get_args(RAW_API_ACTIVITY_TYPES):
        return activity_type

    if "do" in activity_data:
        # activity is a THEN/ELSE block OR a simple Sequence
        return "Sequence"

    return None


class ApiWorkflowParser:
    def __init__(self, activities_retriever: APIActivitiesRetriever):
        self.activities_retriever = activities_retriever
        self.activity_id_counter = defaultdict(int)

    def _create_nested_activities(
        self, activities_list: list[dict[str, Any]], metadata_accumulator: dict[str, Any], activity_id_map: dict[str, str], is_generated: bool = False
    ) -> list[BASE_API_ACTIVITIES]:
        """Convert a list of activities to the appropriate Pydantic models."""

        if is_generated:
            return activities_list  # type: ignore

        if len(activities_list) == 1 and "empty" in activities_list[0]:
            # { "empty": {"set": "${null}" } } - is a placeholder for an empty sequence
            return []

        converted_activities = []
        for activity_dict in activities_list:
            if len(activity_dict) != 1:
                raise ValueError(f"Expected a single activity in an activity block, instead got {len(activity_dict)}")

            activity_id, activity_data = next(iter(activity_dict.items()))
            converted_activity = self._create_activity(activity_id, activity_data, metadata_accumulator, activity_id_map)
            converted_activities.append(converted_activity)

        return converted_activities

    def _create_activity(
        self, activity_id: str, activity_data: dict[str, Any], metadata_accumulator: dict[str, Any], activity_id_map: dict[str, str], is_generated: bool = False
    ) -> BASE_API_ACTIVITIES:
        """Create and configure the appropriate activity based on type."""
        activity_type = _extract_activity_type(activity_data)
        metadata = activity_data.get("metadata", {})
        try:
            metadata_dict = json.loads(metadata.get("configuration", "{}"))
        except json.JSONDecodeError:
            metadata_dict = {}
        if metadata_dict:
            metadata.update(metadata_dict)
        thought = metadata.get("displayName", "")

        metadata_copy = copy.deepcopy(metadata)

        # Create activity based on type with type-specific properties
        if activity_type == "Connector":
            config = json.loads(metadata.get("configuration", "{}"))
            instance_params = config.get("instanceParameters", {})

            activity_type_param = "generic-activity" if instance_params.get("activityType") == "Generic" else "activity"

            object_name = config.get("objectName") if activity_type_param == "generic-activity" else metadata.get("objectName")

            activity_type = self._get_DAP_activity_type(metadata["uiPathActivityTypeId"], object_name, activity_type_param)

            # the id of the activity should be the same with the export property where the output of this prop is saved
            if not is_generated:
                if "export" in activity_data and (export := activity_data["export"].get("as")):
                    match = EXPORT_PROPERTY_PATTERN.search(export)
                    if match is None:
                        raise ValueError(f"Could not extract activity ID from export: {export}")
                    new_activity_id = match.group(1)
                else:
                    # keep the original activity id
                    new_activity_id = activity_id

                if new_activity_id in activity_id_map.values():
                    new_activity_id = f"{new_activity_id}_{self.activity_id_counter[new_activity_id] + 1}"
                    self.activity_id_counter[new_activity_id] += 1

                activity_id_map[activity_id] = new_activity_id
                activity_id = new_activity_id
            else:
                activity_id_map[activity_id] = activity_data["id"]
                activity_id = activity_data["id"]

            # to reduce invalid expressions due to poor escaping; we must replace the single quotes in the method parameter with double quotes
            body_parameters = (
                self._transform_DAP_http_body_parameters(activity_data["with"]["bodyParameters"])
                if object_name == DAP_HTTP_OBJECT_NAME
                else activity_data["with"]["bodyParameters"]
            )

            # Passing a dict to model_validate instead of creating directly to avoid any problems caused by "with" being a keyword
            activity = ConnectorIntegration.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "activity": activity_type,
                    "with": {
                        **activity_data["with"]["pathParameters"],
                        **activity_data["with"]["queryParameters"],
                        **body_parameters,
                    },
                }
            )
            metadata_copy["connector"] = activity_data["with"]["connector"]
            metadata_copy["connectionId"] = activity_data["with"]["connectionId"]
        elif activity_type == "If":
            activity_id_map[activity_id] = activity_id
            # if activities have a deeply nested structure, which we aim to simplify
            nested_raw_activities = activity_data["do"]

            # extract the condition from the switch case - this is the first activity in the do block and should always exist
            _, condition_block = get_sole_item_from_dict(nested_raw_activities[0])
            condition = condition_block["switch"][0]["case"]["when"]

            _, raw_true_sequence = get_sole_item_from_dict(nested_raw_activities[1])
            _, raw_false_sequence = get_sole_item_from_dict(nested_raw_activities[2])

            activity = If.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "activity": IF_ACTIVITY_TYPE,
                    "condition": condition,
                    # these should always be a sequence
                    "then": self._create_nested_activities(raw_true_sequence["do"], metadata_accumulator, activity_id_map, is_generated),
                    "else": self._create_nested_activities(raw_false_sequence["do"], metadata_accumulator, activity_id_map, is_generated),
                }
            )
        elif activity_type == "JsInvoke":
            activity_id_map[activity_id] = activity_id
            activity = JsInvoke(thought=thought, id=activity_id, activity=JS_INVOKE_ACTIVITY_TYPE, code=activity_data["run"]["script"]["code"])
        elif activity_type == "Http":
            activity_id_map[activity_id] = activity_id
            activity = HttpRequest.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "with": activity_data["with"],
                    "activity": HTTP_ACTIVITY_TYPE,
                }
            )
        elif activity_type == "ForEach":
            activity_id_map[activity_id] = activity_id
            # for activity contains a 'do' block with a single for each body activity
            _, body_sequence = get_sole_item_from_dict(activity_data["do"][0])
            # Passing a dict to model_validate instead of creating directly to avoid any problems caused by "with" being a keyword
            activity = ForEach.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "activity": FOR_EACH_ACTIVITY_TYPE,
                    "for": activity_data["for"],
                    "do": self._create_nested_activities(body_sequence["do"], metadata_accumulator, activity_id_map, is_generated),
                }
            )
        elif activity_type == "Response":
            activity_id_map[activity_id] = activity_id
            if "raise" in activity_data:
                type = "FAILURE"
                response_message = activity_data["raise"]["error"]["detail"]
            else:
                type = "SUCCESS"
                response_message = activity_data["set"]
            activity = Response.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "activity": RESPONSE_ACTIVITY_TYPE,
                    "type": type,
                    "response_message": response_message,
                }
            )

        elif activity_type == "TryCatch":
            activity_id_map[activity_id] = activity_id
            try_activities_sequence = activity_data["try"]
            catch_ = activity_data["catch"]

            activity = TryCatch.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "activity": TRY_CATCH_ACTIVITY_TYPE,
                    "try": self._create_nested_activities(try_activities_sequence, metadata_accumulator, activity_id_map, is_generated),
                    "catch": {"as": catch_["as"], "do": self._create_nested_activities(catch_["do"], metadata_accumulator, activity_id_map, is_generated)},
                }
            )
        elif activity_type == "DoWhile":
            activity_id_map[activity_id] = activity_id
            condition = activity_data["doWhile"]
            limit = activity_data.get("limit", None)
            _, body_sequence = get_sole_item_from_dict(activity_data["do"][0])
            activity = DoWhile.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "activity": DO_WHILE_ACTIVITY_TYPE,
                    "condition": condition,
                    "limit": limit,
                    "do": self._create_nested_activities(body_sequence["do"], metadata_accumulator, activity_id_map, is_generated),
                }
            )
        elif activity_type == "Break":
            activity_id_map[activity_id] = activity_id
            activity = Break.model_validate(
                {
                    "thought": thought,
                    "id": activity_id,
                    "activity": BREAK_ACTIVITY_TYPE,
                }
            )
        elif activity_type == "Sequence":
            activity_id_map[activity_id] = activity_id
            activity = self._create_nested_activities(activity_data["do"], metadata_accumulator, activity_id_map, is_generated)
            activity = Sequence(thought=thought, id=activity_id, do=activity, activity=SEQUENCE_ACTIVITY_TYPE)
        else:
            raise ValueError(f"Unknown activity type {activity_type} for activity metadata type {metadata['activityType']}")

        metadata_accumulator[activity_id] = metadata_copy
        return activity

    def _transform_DAP_http_body_parameters(self, body_parameters: dict[str, Any]) -> dict[str, Any]:
        # replace the single quotes in the method parameter with double quotes
        if constants.DAP_HTTP_METHOD_PARAM_NAME in body_parameters:
            method = body_parameters[constants.DAP_HTTP_METHOD_PARAM_NAME].replace("'", '"')

            # Trim ${"..."} wrapper from method if present
            if method.startswith('${"') and method.endswith('"}'):
                method = method[3:-2]

            body_parameters[constants.DAP_HTTP_METHOD_PARAM_NAME] = method

        return body_parameters

    def _get_DAP_activity_type(self, dap_activity_id: str, object_name: str | None, activity_type: ActivityType | None = None) -> str:
        if object_name == DAP_HTTP_OBJECT_NAME and activity_type == "generic-activity":
            activity_id = create_dynamic_activity_full_id(constants.DAP_HTTP_ACTIVITY_NAME, dap_activity_id)
            activity_type = "activity"
        else:
            activity_id = create_dynamic_activity_full_id(constants.DAP_ACTIVITY_NAME, dap_activity_id, object_name, activity_type == "generic-activity")

        activity = self.activities_retriever.get_by_activity_id(activity_id, activity_type)

        if activity is not None:
            return remove_dynamic_activity_prefix_from_name(activity["fullClassName"])

        raise ValueError(f"Unknown DAP activity type {activity_id}")

    def parse_api_workflow(self, api_wf: dict) -> tuple[ApiWorkflow, dict[str, Any], dict[str, str]]:
        """
        Extract the root activity from the API workflow dict and create an ApiWorkflow object.
        Also returns the metadata of the raw workflow.
        """
        if "root" in api_wf:
            # This is a generated workflow, so we should just map the api_wf to the ApiWorkflow object
            root_activity = self._create_activity(api_wf["root"]["id"], api_wf["root"], {}, {}, is_generated=True)
            if not isinstance(root_activity, Sequence):
                raise ValueError("Expected workflow root to be a SequenceActivity")

            # This is a generated workflow, so we should just map the api_wf to the ApiWorkflow object
            return ApiWorkflow(root=root_activity, input=self._get_input_arguments(api_wf)), {}, {}

        # Check main do block
        if len(api_wf["do"]) != 1:
            raise ValueError(f"Expected exactly one value in the main do block, got {len(api_wf['do'])}")

        # Find the root of the workflow
        main_activities_scope = api_wf["do"][0]
        if not isinstance(main_activities_scope, dict) or len(main_activities_scope) != 1:
            raise ValueError("Main activities scope must be a dict with a single inner sequence")

        root_activity_id, root_activity_data = get_sole_item_from_dict(main_activities_scope)
        if not isinstance(root_activity_data, dict):
            raise ValueError("Expected workflow root to be a dict")

        metadata, activity_id_map = {}, {}
        root_activity = self._create_activity(root_activity_id, root_activity_data, metadata, activity_id_map)
        if not isinstance(root_activity, Sequence):
            raise ValueError("Expected workflow root to be a SequenceActivity")

        # Create the ApiWorkflow object
        api_workflow = ApiWorkflow(root=root_activity, input=self._get_input_arguments(api_wf))

        return api_workflow, metadata, activity_id_map

    def _get_input_arguments(self, api_wf: dict) -> str:
        """
        Extract the input arguments from the API workflow dict.
        """
        if (
            "input" in api_wf
            and "schema" in api_wf["input"]
            and "format" in api_wf["input"]["schema"]
            and api_wf["input"]["schema"]["format"] == "json"
            and "document" in api_wf["input"]["schema"]
        ):
            input_schema = api_wf["input"]["schema"]["document"]
            return json.dumps(input_schema)

        return '{ "type": "object", "properties": {} }'


def list_activities(workflow: ApiWorkflow | ApiWorkflowForActivityEdit) -> list[BASE_API_ACTIVITIES]:
    """
    Iteratively traverses the workflow and returns a flat list of all activities.
    """
    all_activities: list[BASE_API_ACTIVITIES] = []
    activities_to_visit: list[BASE_API_ACTIVITIES] = [workflow.root]
    while activities_to_visit:
        current_activity = activities_to_visit.pop()
        all_activities.append(current_activity)
        if isinstance(current_activity, (Sequence, ForEach)):
            activities_to_visit.extend(reversed(current_activity.do))
        elif isinstance(current_activity, If):
            activities_to_visit.extend(reversed(current_activity.then))
            activities_to_visit.extend(reversed(current_activity.else_))
        elif isinstance(current_activity, TryCatch):
            activities_to_visit.extend(reversed(current_activity.try_))
            activities_to_visit.extend(reversed(current_activity.catch.do))
    return all_activities


def list_activities_post_gen(workflow: PostGenApiWorkflow) -> list[POST_GENERATION_ACTIVITIES]:
    """
    Iteratively traverses the workflow and returns a flat list of all activities.
    """
    all_activities: list[POST_GENERATION_ACTIVITIES] = []
    activities_to_visit: list[POST_GENERATION_ACTIVITIES] = [workflow.root]
    while activities_to_visit:
        current_activity = activities_to_visit.pop()
        all_activities.append(current_activity)
        if isinstance(current_activity, (PostGenSequence, PostGenForEach)):
            activities_to_visit.extend(reversed(current_activity.do))
        elif isinstance(current_activity, PostGenIf):
            activities_to_visit.extend(reversed(current_activity.then))
            activities_to_visit.extend(reversed(current_activity.else_))
        elif isinstance(current_activity, PostGenTryCatch):
            activities_to_visit.extend(reversed(current_activity.try_))
            activities_to_visit.extend(reversed(current_activity.catch.do))
    return all_activities


def try_find_activity_by_id(workflow: ApiWorkflow | ApiWorkflowForActivityEdit, activity_id: str) -> BASE_API_ACTIVITIES | None:
    """
    Find an activity by its ID.
    """
    for activity in list_activities(workflow):
        if activity.id == activity_id:
            return activity
    return None


def find_activity_by_id(workflow: ApiWorkflow | ApiWorkflowForActivityEdit, activity_id: str) -> BASE_API_ACTIVITIES:
    activity = try_find_activity_by_id(workflow, activity_id)
    if activity is None:
        raise ValueError(f"Activity with ID {activity_id} not found in workflow")
    return activity
