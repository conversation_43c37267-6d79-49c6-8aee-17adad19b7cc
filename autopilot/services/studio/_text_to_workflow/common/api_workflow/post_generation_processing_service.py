import pathlib

from langchain.output_parsers import YamlOutputParser
from langchain.output_parsers.fix import OutputFixingParser
from langchain.prompts import PromptTemplate
from langchain_core.language_models import BaseChatModel
from langchain_core.output_parsers import JsonOutputParser

from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.output_parsers import NON_ESCAPABLE_KEYS, YAML_CAPTURE_PATTERN, build_api_wf_pydantic_parser_with_fix
from services.studio._text_to_workflow.common.api_workflow.post_generation_api_wf_parser import PostGenerationApiWfParser
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import (
    ApiWorkflowPostProcessingDetails,
    PostGenApiWorkflow,
    PostGenSequence,
)
from services.studio._text_to_workflow.common.api_workflow.schema import (
    API_WF_EXPRESSION_LANGUAGE,
    ApiWorkflow,
    ConnectorIntegration,
)
from services.studio._text_to_workflow.common.api_workflow.workflow_parser import list_activities
from services.studio._text_to_workflow.common.schema import ActivityDefinition, Connection
from services.studio._text_to_workflow.utils.activity_utils import remove_dynamic_activity_prefix_from_name
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import enforce_single_quotes_on_yaml_values, yaml_load
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    WorkflowProcessingError,
    WorkflowProcessingErrorType,
)

LOGGER = AppInsightsLogger()

API_WF_FIXER_PROMPT_CONFIG = yaml_load((pathlib.Path(__file__).parent).absolute() / "fix_api_wf_prompt.yaml")
DRAFT_PROMPT_CONFIG = yaml_load((pathlib.Path(__file__).parent.parent.parent).absolute() / "api_workflow/config/api_draft_creation_prompt.yaml")


class ApiWfPostGenerationProcessingService:
    """Handles API workflow deserialization and coordinates validation."""

    def __init__(
        self,
        activity_retriever: APIActivitiesRetriever,
    ):
        self.activity_retriever = activity_retriever

    async def _parse_raw_workflow(
        self, raw_workflow: str, model: BaseChatModel | None, expression_language: API_WF_EXPRESSION_LANGUAGE, proposed_activities: list[str]
    ) -> ApiWorkflow | None:
        """
        Parses a raw API workflow and returns a valid ApiWorkflow object.
        """
        parser = YamlOutputParser(pydantic_object=ApiWorkflow, pattern=YAML_CAPTURE_PATTERN)
        try:
            # try to parse the raw workflow using the pydantic parser
            # exceptions here are expected and we retry the parsing, so we want to ignore them for the most part unless the parser fails entirely
            workflow: ApiWorkflow = parser.invoke(raw_workflow)  # @IgnoreException
            return workflow
        except Exception:
            LOGGER.warning("Basic parser failed, trying output fixing single quotes.")
            # try to fix invalid quotes escaping
            try:
                raw_workflow_fix_escaped = raw_workflow.replace("\\'", "''")
                workflow_fix_escaped: ApiWorkflow = parser.invoke(raw_workflow_fix_escaped)
                return workflow_fix_escaped
            except Exception:
                pass

            # try to fix invalid input arguments
            raw_workflow = self._validate_input_arguments(raw_workflow)

            if model is not None:
                # Create enhanced api wf YAML parser with single quote validation and schema enforcement
                workflow_parser = build_api_wf_pydantic_parser_with_fix(
                    model,
                    ApiWorkflow,
                    self._build_yaml_fixer_prompt(proposed_activities, expression_language),
                )
                try:
                    workflow_fix: ApiWorkflow = workflow_parser.invoke(raw_workflow)
                    return workflow_fix
                except Exception as e:
                    LOGGER.warning(f"Output parsers with llm fix failed. Exception {e}")

            # we only try to fix the raw_workflow by enforcing single quotes on the expressions
            raw_workflow_with_quotes = enforce_single_quotes_on_yaml_values(raw_workflow, non_escapeable_keys=NON_ESCAPABLE_KEYS)
            try:
                workflow_with_quotes: ApiWorkflow = parser.invoke(raw_workflow_with_quotes)  # @IgnoreException
                return workflow_with_quotes
            except Exception:
                pass

        return None

    @staticmethod
    def _build_yaml_fixer_prompt(proposed_activities: list[str], expression_language: API_WF_EXPRESSION_LANGUAGE) -> PromptTemplate:
        """Build a prompt for the YAML fixer."""

        # build the integration activity names section (we specifically want to mention that these are valid activity names)
        if len(proposed_activities) > 0:
            formated_proposed_activities = [
                API_WF_FIXER_PROMPT_CONFIG["activity_name_template"].format(activity_name=activity) for activity in proposed_activities
            ]
            integration_activity_names_section = API_WF_FIXER_PROMPT_CONFIG["activity_name_section_template"].format(
                api_integration_activities="\n".join(formated_proposed_activities),
                example_activity_name=proposed_activities[0],
            )
        else:
            integration_activity_names_section = ""

        return PromptTemplate.from_template(
            API_WF_FIXER_PROMPT_CONFIG["prompt"],
            partial_variables={
                "integration_activity_names_section": integration_activity_names_section,
                "language_name": DRAFT_PROMPT_CONFIG[expression_language]["language_name"],
            },
        )

    @staticmethod
    def _validate_input_arguments(raw_workflow: str) -> str:
        # Sometimes, the input arguments will be an empty JSON object, which will cause the pydantic parser to fail.
        # we will handle this by replacing the empty JSON object with a string representing an empty JSON object

        # segments the raw workflow
        segments = raw_workflow.splitlines()
        if len(segments) >= 1 and segments[1].rstrip() == "input: {}":
            segments[1] = segments[1] = 'input: \'{"type": "object", "properties": {}}\''
            return "\n".join(segments)

        return raw_workflow

    async def process_raw_workflow(
        self,
        raw_workflow: str,
        model: BaseChatModel | None,  # if None, we will not attempt to fix the workflow or input arguments
        config_map: dict[str, dict],
        expression_language: API_WF_EXPRESSION_LANGUAGE,
        connections_by_key: dict[str, Connection],
        activity_definitions: list[ActivityDefinition],
        clear_invalid_expressions: bool = True,
        output_type_definitions: str | None = None,
        is_model_retry_permitted: bool = True,
        can_augment_configmap: bool = True,
    ) -> ApiWorkflowPostProcessingDetails:
        """
        Parses a raw API workflow and validates all expressions in it.
        Returns a processed workflow with validation metadata and errors.
        """
        activity_names = [remove_dynamic_activity_prefix_from_name(activity["fullClassName"]) for activity in activity_definitions]
        workflow = await self._parse_raw_workflow(raw_workflow, model, expression_language, activity_names)
        if workflow is None:
            return ApiWorkflowPostProcessingDetails(
                raw_workflow=raw_workflow,
                generated_workflow=None,
                processed_workflow=None,
                post_generation_errors=[WorkflowProcessingError(workflow_path=[], type=WorkflowProcessingErrorType.INVALID_STRUCTURE)],
            )

        if can_augment_configmap:
            await self._augment_configmap_with_not_retrieved_activities(config_map, connections_by_key, activity_definitions, workflow)

        # Create a parser to validate and process the workflow
        parser = PostGenerationApiWfParser(
            activity_retriever=self.activity_retriever,
            expression_language=expression_language,
            config_map=config_map,
            clear_invalid_expressions=clear_invalid_expressions,
            connections_by_key=connections_by_key,
            output_type_definitions=output_type_definitions,
        )

        # Process the workflow activities
        processed_activities = await parser.process_activities(workflow.root.do, is_model_retry_permitted=is_model_retry_permitted)

        # Build the post-gen scope activity for the root
        post_gen_scope_activity = PostGenerationApiWfParser.build_post_gen_scope_activity(workflow.root, processed_activities)
        assert isinstance(post_gen_scope_activity, PostGenSequence)

        # Create the processed workflow
        input_parser = JsonOutputParser()

        # just in case the input is not parsed correctly, we will set it to a default object
        input = None
        try:
            input = input_parser.parse(workflow.input)
        except Exception:
            try:
                if model is not None:
                    input = OutputFixingParser.from_llm(parser=input_parser, llm=model, prompt=API_WF_FIXER_PROMPT_CONFIG["json_schema_fixer_prompt"]).parse(
                        workflow.input
                    )
            except Exception:
                LOGGER.warning("Basic parser failed, trying output fixing single quotes.")

        # if the input is not a dict, we will set it to a default object
        if not isinstance(input, dict):
            input = {"type": "object", "properties": {}, "required": [], "additionalProperties": False}

        processed_workflow = PostGenApiWorkflow(
            input=input,
            root=post_gen_scope_activity,
        )

        # Return all validation details
        return ApiWorkflowPostProcessingDetails(
            raw_workflow=raw_workflow,
            generated_workflow=workflow,
            processed_workflow=processed_workflow,
            post_generation_errors=parser.errors,
        )

    async def _augment_configmap_with_not_retrieved_activities(
        self, config_map: dict[str, dict], connections_by_key: dict[str, Connection], activity_definitions: list[ActivityDefinition], workflow: ApiWorkflow
    ):
        workflow_activities = list_activities(workflow)
        activity_definitions_dict = {a["fullClassName"].replace(constants.DAP_NAMESPACE + ".", ""): a for a in activity_definitions}

        additional_required_definitions = []
        for workflow_activity in workflow_activities:
            if isinstance(workflow_activity, ConnectorIntegration) and workflow_activity.activity not in activity_definitions_dict:
                additional_required_definitions.append(constants.DAP_NAMESPACE + "." + workflow_activity.activity)

        not_retrieved_activity_defs = [
            act for act in (self.activity_retriever.get_by_class_name(a) for a in additional_required_definitions) if act is not None
        ]

        if len(not_retrieved_activity_defs) == 0:
            return

        # Get DAP metadata for activities that were not proposed, but the model used them based on the demonstrations
        _, additional_config_map = await WorkflowGenerationDynamicActivitiesComponent.augment_type_definitions_for_dynamic_activities(
            {activity["fullClassName"]: activity for activity in not_retrieved_activity_defs},
            {},
            connections_by_key,
            force_is_package_name=constants.FORCED_IS_PACKAGE_NAME,
            force_is_package_version=constants.FORCED_IS_PACKAGE_VERSION,
        )

        config_map.update(additional_config_map)
