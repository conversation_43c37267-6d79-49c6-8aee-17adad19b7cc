import base64
import mimetypes
from dataclasses import dataclass

import numpy as np
import typing_extensions as t
from pydantic import BaseModel, Field

MetaType: t.<PERSON>Alias = t.Literal["class", "interface", "enum", "record struct", "struct", "record"]
ParamTypeCategory: t.<PERSON><PERSON> = t.Literal[
    "InArgument",
    "OutArgument",
    "InOutArgument",
    "ListInArgument",
    "DictionaryInArgument",
    "ListOutArgument",
    "DictionaryOutArgument",
    "Property",
    "Accessor",
    "ActivityAction",
    "Activity",
]
ParamValueCategory = (
    t.Literal[
        "variable",
        "expression",
        "activity_action",
        "activity_sequence",
        "primitive_property",
        "dict_property",
        "list_property",
    ]
    | None
)

# This map is used to determine if a given value category is compatible with a given parameter type category.
PARAM_VALUE_CATEGORY_COMPATIBILITIES_MAP: dict[ParamTypeCategory, list[ParamValueCategory]] = {
    "InArgument": [
        "primitive_property",
        "variable",
        "expression",
        "list_property",
        "dict_property",
    ],
    "OutArgument": ["variable", "expression"],
    "InOutArgument": ["variable", "expression"],
    "ListInArgument": ["variable", "expression", "list_property"],
    "DictionaryInArgument": ["variable", "expression", "dict_property"],
    "ListOutArgument": ["variable", "expression"],
    "DictionaryOutArgument": ["variable", "expression"],
    "ActivityAction": ["activity_action", "activity_sequence"],
    "Activity": ["activity_action", "activity_sequence"],
}

ArgumentDirection: t.TypeAlias = t.Literal["In", "Out", "InOut"]
TargetFramework: t.TypeAlias = t.Literal["Portable", "Windows", "Api", "All"]
SubsetName: t.TypeAlias = t.Literal["train", "test", "prod", "prod-sample", "static", "uia", "testcase", "prod-filtered"]
ActivityType: t.TypeAlias = t.Literal["activity", "trigger", "generic-activity", "generic-trigger"]
ConnectionState: t.TypeAlias = t.Literal["Enabled", "Disabled", "Expired", "Failed"] | None
Role: t.TypeAlias = t.Literal["system", "user", "assistant"]
InputPropertyLocation: t.TypeAlias = t.Literal["Query", "Body", "Path", "Multipart"]  # Multipart is not supported yet


class ParamTypeDef(t.TypedDict):
    name: str
    type: str
    ctype: t.NotRequired[str]
    modifier: str
    description: str
    required: bool
    category: ParamTypeCategory
    components: list[str]
    values: t.NotRequired[list[str]]


class TypeDef(t.TypedDict):
    name: str
    type: MetaType
    description: str
    type_params: dict[str, str]
    params: dict[str, ParamTypeDef]
    text: str


class WorkflowConversionTypeDef(t.TypedDict):
    Succes: bool
    TypeDefinition: str
    AdditionalTypeDefinitions: str
    AdditionalTypeNamespaces: str


class Package(t.TypedDict):
    name: str
    version: str


class Variable(t.TypedDict):
    name: str
    type: str
    scopeActivityId: t.NotRequired[str]


class Argument(t.TypedDict):
    direction: ArgumentDirection
    name: str
    type: str


class ActivityDict(t.TypedDict):
    thought: str
    activity: str
    params: dict[str, t.Any]
    id: t.NotRequired[str]
    currentActivity: t.NotRequired[bool]
    displayName: t.NotRequired[str]


class ActivityDelegateDict(t.TypedDict):
    variables: list[Variable]
    Handler: list[ActivityDict]


class WorkflowDict(t.TypedDict):
    processName: str
    packages: list[str]
    trigger: ActivityDict
    workflow: list[ActivityDict]
    variables: list[Variable]
    arguments: t.NotRequired[list[Argument]]
    namespaceImports: t.NotRequired[list[str]]


WorkflowVariables: t.TypeAlias = dict[
    t.Literal[
        "in_scope",
        "out_of_scope",
        "defined_by_arguments",
        "defined_by_user",
        "defined_by_activity",
        "defined_by_activity_delegate",
    ],
    dict[str, Variable],
]


class InputProperty(t.TypedDict):
    # The name of the property as it appears in the DAP activity.
    name: str
    # The name of the property as it should appear in the workflow.
    schema_name: str
    # The type of the property.
    type: str
    # The location of the property.
    location: t.NotRequired[InputPropertyLocation]
    # The description of the property.
    description: t.NotRequired[str]
    # Whether the property is required.
    required: bool
    # Whether the property is visible by default in the UI.
    visibleByDefault: bool | None


class OutputType(t.TypedDict):
    type: str  # the type of the output object
    definitions: dict[str, t.Any]  # the type definitions of the output object and nested objects
    isArray: bool


class ActivityDefinition(t.TypedDict):
    # The numeric ID of the activity as it appears in the database
    id: int
    # The index of the activity in the list of activities. Used to find corresponding embedding in the embbeding matrix.
    index: int
    # The full activity identifier in the format `namespace.className@typeid`.
    # The generic suffix is removed from the class name.
    # The typeid suffix is present only for dynamic activities. (i.e. activities that have a UipathTypeId)
    # This is the preferred way to identify an activity.
    fullActivityId: str
    # The fully qualified activity name as it appears in the embeddings db with the generic suffix removed.
    # In the case of static activities, this should coincide with the `fullActivityId`.
    fullActivityName: str
    # The full class name of the activity in the format `namespace.className`.
    # In the case of static activities, this should coincide with `fullActivityName` and `fullActivityId`.
    # In the case of dynamic activities, this is the alias that we use to refer to the activity when generation the workflow LM-YAML.
    fullClassName: str
    # The class name of the activity
    className: str
    # The namespace of the activity
    namespace: str
    # The package to which the activity belongs
    packageName: str
    # The version of the package to which the activity belongs
    packageVersion: str
    # The display name of the activity. This is collected by the activity extraction pipeline.
    displayName: str
    # The description of the activity. This is collected by the activity extraction pipeline and is possibly rewritten using an LLM.
    description: str
    # The categories for an activity. This is collected by the activity extraction pipeline and possibly inferred using an LLM.
    category: str
    # The type definition of the activity.
    typeDefinition: str
    # Type definitions of any additional complex objects used in the activity type definition.
    additionalTypeDefinitions: str
    # Namespaces of any additional complex objects used in the activity type definition.
    additionalNamespaces: str
    # A boolean that indicates whether the activity is a trigger or not.
    isTrigger: bool
    # The parameters of the activity, parsed from `typeDefinition`.
    params: dict[str, ParamTypeDef]
    # The generic components of the parameters with generic types.
    genericParamDefinition: dict[str, str]
    # The key of the connection type used by Integration Service to configure a dynamic activity.
    connectorKey: str | None
    # The identifier used by Integration Service to configure a dynamic activity.
    activityTypeId: str | None
    # A JSON string that contains the Integration Service configuration for a dynamic activity. Defaults to a blank configuration. Updated during inference.
    activityConfiguration: str | None
    # The default stored configuration string required for retrieving the initial discovery result.
    defaultConfiguration: str | None
    # A boolean indicating whether the activity was successfully configured by Integration Service. Set during inference for dynamic activities.
    activityIsConfigured: bool | None
    # A string that contains the details of a dynamic activity. Updated during inference for dynamic activities.
    dynamicActivityDetails: str | None
    # The identifier of a connection used by Integration Service to configure a dynamic activity. Updated during inference for dynamic activities.
    connectionId: str | None
    # The similarity of the activity to the query. Updated during inference.
    similarity: float | None
    # The plan step at which the activity was retrieved. Updated during inference.
    step: int | None
    # Unique ID used for restoring DAP information.
    uuid: str | None
    # The JSON schema of the activity and its underlying properties.
    jsonSchema: str | None
    # The input properties of the activity.
    inputPropertiesJsonSchema: list[InputProperty] | None
    # The structure of the output object of the activity (supported only for dynamic activities).
    outputTypeJsonSchema: OutputType | None
    # The HTTP method used to send the request corresponding to the activity (supported only for dynamic activities).
    httpMethod: str | None
    # The name of the object that the activity is built with.
    objectName: str | None
    # Whether the activity is a generic activity.
    genericActivity: bool | None
    # These are remapped categories and names that are more concise and human-readable.
    mapped_category: t.NotRequired[str]
    mapped_name: t.NotRequired[str]
    package_display_name: t.NotRequired[str]


class CodeMethodDefinition(t.TypedDict):
    id: int
    name: str
    packageName: str
    typeFullName: str
    typeMethodName: str
    description: str
    definitions: str
    additionalTypeDefinitions: str
    namespaceImports: str
    similarity: float | None


class PlanStep(t.TypedDict):
    text: str
    embedding: list[float]
    triggers: list[ActivityDefinition]
    activities: list[ActivityDefinition]


class ActivityRetrieverState(t.TypedDict):
    activities: list[ActivityDefinition]
    display_name_embeddings: np.ndarray
    description_embeddings: np.ndarray
    category_embeddings: np.ndarray
    fullactivityid2index: dict[str, int]
    index2fullactivityid: dict[int, str]
    fullclassname2index: dict[str, int]
    index2fullclassname: dict[int, str]
    classname2fullclassnames: dict[str, list[str]]


class Connection(t.TypedDict, total=False):
    connectionId: str | None
    connector: t.Required[str]
    friendlyName: str
    state: ConnectionState
    isShared: bool
    isDefault: bool
    embedding: list[float]


UIObjectType: t.TypeAlias = t.Literal["App", "Screen", "Element"]


class UIObject(t.TypedDict, total=False):
    name: str
    description: str | None
    descriptor: t.NotRequired[str | None]
    taxonomyType: str | None
    type: UIObjectType
    reference: str
    children: list["UIObject"] | None


ActivitiesGenerationMode = t.Literal["workflow", "sequence", "testcase", "edit"]


class ActivitySearchOptions(t.TypedDict):
    target_framework: TargetFramework
    ignored_namespaces: set[str]
    ignored_activities: set[str]
    mode: ActivitiesGenerationMode | None
    generic_activities: bool


class Message(t.TypedDict):
    role: Role
    content: str


class WorkflowError(BaseModel):
    activityId: str
    message: str
    property: str | None = None


class WorkflowFileMetadata(BaseModel):
    description: str | None = None
    isTestCase: bool = False
    isMain: bool = False
    isEntryPoint: bool = False
    errors: list[WorkflowError] | None = None
    lastModified: str | None = None


# Try to import magic, but don't fail if not available
try:
    import magic

    MAGIC_AVAILABLE = True
except ImportError:
    print("Magic is not available for mime type detection. Install libmagic to enable it.")
    MAGIC_AVAILABLE = False


class File(BaseModel):
    path: str
    content: str  # base64 encoded content
    metadata: dict | WorkflowFileMetadata = Field(default_factory=dict)
    _mime_type: str | None = None

    @property
    def mime_type(self) -> str:
        # Lazily detect the mime type
        if self._mime_type is None:
            self._mime_type = self._detect_mime_type()
        return self._mime_type

    @mime_type.setter
    def mime_type(self, value: str | None) -> None:
        self._mime_type = value

    def _detect_mime_type(self) -> str:
        # First try to detect from file extension
        mime_type, _ = mimetypes.guess_type(self.path)
        if mime_type:
            return mime_type

        # Fallback: try to detect from content if possible
        if MAGIC_AVAILABLE:
            try:
                # Decode a small sample of the content to check
                content_sample = base64.b64decode(self.content[:2048])
                return magic.from_buffer(content_sample, mime=True)
            except Exception:
                pass

        # If all else fails, return a generic binary type
        return "application/octet-stream"


@dataclass
class EmbeddingsActivityFilterOptions:
    """
    Filter criteria for activities when loading from the embeddings database.
    Controls which activities should be included in the loaded embeddings.
    """

    allowed_activity_names: set[str] | None = None
    include_dynamic_http_activities: bool = False
    activities_to_exclude: set[str] | None = None
    generic_activities_only: bool = False
    curated_activities_only: bool = False


@dataclass
class FolderDefinition:
    """Definition of an Orchestrator folder with its properties."""

    fqn: str  # Fully qualified name used by activities to refer to the folder.
    has_children: bool
    id: int
    description: t.Optional[str]


@dataclass
class AgentDefinition:
    """Definition of an orchestrator agent process with its properties."""

    id: int
    key: str
    name: str
    description: t.Optional[str]
    arguments_schema: dict  # mapped from ArgumentsV2
    folder: t.Optional[FolderDefinition] = None
