parameters:
  - name: runExperimentalTests
    displayName: 'Run experimental tests'
    type: boolean
    default: false

name: v$(Date:yyyy.MM.dd)_$(Rev:rr)

pr:
  drafts: false
  branches:
    include:
      - master
      - release/autopilot/*
  paths:
    include:
      - autopilot/*

resources:
  repositories:
    - repository: Autopilot.Samples
      type: github
      endpoint: UiPath
      name: UiPath/Autopilot.Samples
      ref: refs/heads/release/wingman/250703

trigger:
  batch: true
  branches:
    include:
      - release/autopilot/*

pool: "Autopilot-Developers-Test-Agents"

stages:
  - stage: "TEST"
    jobs:
      - job: "run"
        steps:
        - script: |
            set -x
            ip a
            ip a | grep -q "********/24" && ip a del ********/24 dev eth0 2>/dev/null || true
            ip a
          displayName: 'Cleanup agents IP'
        - template: pipeline.common.yml

        - task: DownloadSecureFile@1
          name: gcpCredentials
          displayName: 'Download GCP credentials'
          inputs:
            secureFile: 'gcp.json'

        - task: Docker@2
          displayName: Build Test Image
          condition: succeededOrFailed()
          inputs:
            command: build
            context: ml/autopilot
            dockerfile: ml/autopilot/Dockerfile
            arguments: |
              --target test
              --tag autopilot:test
              --build-arg UIPATH_AZURE_DEVOPS_ACCESS_TOKEN=$(System.AccessToken)

        - task: Docker@2
          displayName: Run Pytest Services
          condition: succeededOrFailed()
          inputs:
            command: run
            arguments: |
              -e AZURE_OPENAI_ENDPOINT=$(gpt-api-base)
              -e AZURE_OPENAI_API_KEY=$(gpt-api-key)
              -e CV_URL=$(cv-url)
              -e CV_LICENSE_KEY=$(cv-license-key)
              -e OPENAI_CU_API_KEY=$(openai-computer-use-api-key)
              -e PERPLEXITY_API_KEY=$(perplexity-api-key)
              -e USE_LLM_GATEWAY=true
              -e RUN_EMBEDDINGS_BACKGROUND_REBUILD=false
              -e ENABLE_TELEMETRY=false
              -e IS_PROD=false     
              -e CLOUD_URL_BASE="https://alpha.uipath.com"
              -e TEST_ORGANIZATION_ID="4cd25586-e4c7-4d63-ae2b-b990663908ae"
              -e TEST_TENANT_ID="7751aee1-3ac2-4280-8d9f-e5075c7cde28"
              -e S2S_CLIENT_ID="65E61F8C-F740-4A83-B2FF-EBE91EFE7AE8"
              -e S2S_CLIENT_SECRET=$(s2s-client-secret)
              --gpus all
              -v $(Build.SourcesDirectory)/ml/autopilot/.data:/workspace/src/.data
              -v $(gcpCredentials.secureFilePath):/tmp/gcp.json
              autopilot:test pytest --numprocesses 2 --junit-xml=/workspace/src/.data/test_results_services.xml services

        - task: Docker@2
          displayName: Build Dev Image
          condition: succeededOrFailed()
          inputs:
            command: build
            context: ml/autopilot
            dockerfile: ml/autopilot/Dockerfile
            arguments: |
              --target dev
              --tag autopilot:dev
              --build-arg UIPATH_AZURE_DEVOPS_ACCESS_TOKEN=$(System.AccessToken)

        - task: Docker@2
          displayName: Run Pytest Experimental
          condition: and(succeededOrFailed(), eq('${{ parameters.runExperimentalTests }}', true))
          inputs:
            command: run
            arguments: |
              -e AZURE_OPENAI_ENDPOINT=$(gpt-api-base)
              -e AZURE_OPENAI_API_KEY=$(gpt-api-key)
              -e CV_URL=$(cv-url)
              -e CV_LICENSE_KEY=$(cv-license-key)
              -e OPENAI_CU_API_KEY=$(openai-computer-use-api-key)
              -e PERPLEXITY_API_KEY=$(perplexity-api-key)
              -e USE_LLM_GATEWAY=true
              -e RUN_EMBEDDINGS_BACKGROUND_REBUILD=false
              -e ENABLE_TELEMETRY=false
              -e IS_PROD=false
              -e CLOUD_URL_BASE="https://alpha.uipath.com"
              -e TEST_ORGANIZATION_ID="4cd25586-e4c7-4d63-ae2b-b990663908ae"
              -e TEST_TENANT_ID="7751aee1-3ac2-4280-8d9f-e5075c7cde28"
              -e S2S_CLIENT_ID="65E61F8C-F740-4A83-B2FF-EBE91EFE7AE8"
              -e S2S_CLIENT_SECRET=$(s2s-client-secret)
              --gpus all
              -v $(Build.SourcesDirectory)/ml/autopilot/.data:/workspace/src/.data
              -v $(gcpCredentials.secureFilePath):/tmp/gcp.json
              autopilot:dev pytest --numprocesses 2 --junit-xml=/workspace/src/.data/test_results_experimental.xml experimental

        - task: PublishTestResults@2
          displayName: Publish Test Results
          condition: succeededOrFailed()
          inputs:
            testResultsFormat: JUnit
            testResultsFiles: $(Build.SourcesDirectory)/ml/autopilot/.data/test_results_*.xml

        - task: Docker@2
          displayName: Run Ruff Check
          condition: succeededOrFailed()
          inputs:
            command: run
            arguments: |
              autopilot:dev ruff check

        - task: Docker@2
          displayName: Run Ruff Format Check
          condition: succeededOrFailed()
          inputs:
            command: run
            arguments: |
              autopilot:dev ruff format --check

        - task: Docker@2
          displayName: Run Pyright
          condition: succeededOrFailed()
          inputs:
            command: run
            arguments: |
              autopilot:dev basedpyright
