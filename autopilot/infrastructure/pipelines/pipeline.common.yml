steps:
- checkout: self
  displayName: Checkout ML
  clean: true
  workspaceRepo: true
  lfs: true
  fetchDepth: 1
  fetchTags: false
  sparseCheckoutDirectories: autopilot
  path: s/ml
- checkout: Autopilot.Samples
  displayName: Checkout Autopilot.Samples
  clean: true
  lfs: true
  fetchDepth: 1
  fetchTags: false
  path: s/ml/autopilot/.data/Autopilot.Samples
- task: AzureKeyVault@2
  displayName: Get KeyVault Secrets
  inputs:
    azureSubscription: AzureDevTestEASubscription
    KeyVaultName: ml-dev-gptproxy-kv
    SecretsFilter: "*"
- task: UniversalPackages@0
  displayName: Download Embeddings Model
  inputs:
    command: download
    vstsFeed: ML-models
    vstsFeedPackage: studio-api-sentence-embeddings-model
    vstsPackageVersion: 0.0.5
    downloadDirectory: $(Build.SourcesDirectory)/ml/autopilot/.data/Models/stella-trained
- task: UniversalPackages@0
  displayName: Download Rerank Cross Encoder Model
  inputs:
    command: download
    vstsFeed: ML-models
    vstsFeedPackage: studio-api-sentence-cross-encoder-model
    vstsPackageVersion: 1.0.0
    downloadDirectory: $(Build.SourcesDirectory)/ml/autopilot/.data/Models/cross-encoder
- task: UniversalPackages@0
  displayName: Download DAP CLI
  inputs:
    command: download
    vstsFeed: ML-models
    vstsFeedPackage: dynamic.activities.discovery
    vstsPackageVersion: 25.0.0-alpha.20622
    downloadDirectory: $(Build.SourcesDirectory)/ml/autopilot/.data/DotNet/dynamic.activities.discovery
